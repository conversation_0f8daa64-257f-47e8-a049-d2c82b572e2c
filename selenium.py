# -*- coding: utf-8 -*-
"""
KỊCH BẢN TỰ ĐỘNG ĐĂNG NHẬP VỚI SELENIUM VÀ RECAPTCHA V3
---------------------------------------------------------
Yêu cầu:
pip install selenium webdriver-manager selenium-stealth

Lưu ý:
- <PERSON><PERSON> thuật này phức tạp và có thể không ổn định nếu trang web thay đổi.
- Để có độ tin cậy cao nhất trong môi trường production, hãy cân nhắc dùng dịch vụ giải CAPTCHA.
"""

import time
import random
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.service import Service as ChromeService
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager
from selenium_stealth import stealth

# =====================================================================================
# <<< THAY ĐỔI CÁC GIÁ TRỊ NÀY CHO PHÙ HỢP VỚI TRANG WEB CỦA BẠN >>>
# =====================================================================================
LOGIN_URL = "https://portal.einvoice.fpt.com.vn/"  # Thay bằng URL trang đăng nhập thực tế
MASOTHUE = "0106512255"
USERNAME = "hhuong"             # Thay bằng username của bạn
PASSWORD = "eb6e8a09"                 # Thay bằng password của bạn

# --- Cấu hình Selector (quan trọng nhất!) ---
# Sử dụng By.ID, By.NAME, By.XPATH, hoặc By.CLASS_NAME...
# Ví dụ: (By.ID, "user-name"), (By.NAME, "password"), (By.XPATH, "//button[@type='submit']")
MASOTHUE_SELECTOR = (By.XPATH, "/html/body/div[3]/div[2]/div[2]/div[2]/div/div[3]/div/input")
USERNAME_SELECTOR = (By.XPATH, "/html/body/div[3]/div[2]/div[2]/div[2]/div/div[4]/div/input")
PASSWORD_SELECTOR = (By.XPATH, "/html/body/div[3]/div[2]/div[2]/div[2]/div/div[5]/div/input")
LOGIN_BUTTON_SELECTOR = (By.XPATH, "/html/body/div[3]/div[2]/div[2]/div[2]/div/div[6]/div[1]/div/button")

# --- Selector để xác nhận đăng nhập thành công ---
# Tìm một element chỉ xuất hiện SAU KHI đăng nhập thành công (ví dụ: avatar, dashboard...)
SUCCESS_ELEMENT_SELECTOR = (By.CLASS_NAME, "webix_disabled_view")
# =====================================================================================


def natural_typing(element, text: str):
    """Mô phỏng hành vi gõ phím tự nhiên của người dùng."""
    for char in text:
        element.send_keys(char)
        time.sleep(random.uniform(0.05, 0.15))

def main():
    """Hàm chính thực thi toàn bộ luồng đăng nhập."""
    options = webdriver.ChromeOptions()
    options.add_argument("start-maximized")
    # options.add_argument("--headless") # Bỏ comment dòng này để chạy ẩn, nhưng dễ bị phát hiện hơn.
    options.add_experimental_option("excludeSwitches", ["enable-automation"])
    options.add_experimental_option('useAutomationExtension', False)

    service = ChromeService(ChromeDriverManager().install())
    driver = webdriver.Chrome(service=service, options=options)

    # Áp dụng "tàng hình" để tránh bị phát hiện
    stealth(driver,
            languages=["en-US", "en"],
            vendor="Google Inc.",
            platform="Win32",
            webgl_vendor="Intel Inc.",
            renderer="Intel Iris OpenGL Engine",
            fix_hairline=True,
            )

    try:
        print(">>> Bắt đầu quá trình đăng nhập tự động...")
        driver.get(LOGIN_URL)
        wait = WebDriverWait(driver, 20) # Tăng thời gian chờ nếu mạng chậm

        print(f"1. Đã truy cập trang: {LOGIN_URL}")
        time.sleep(random.uniform(2, 4)) # Chờ trang tải và script reCAPTCHA khởi chạy

        # 2. Tìm và điền thông tin đăng nhập
        username_field = wait.until(EC.presence_of_element_located(USERNAME_SELECTOR))
        password_field = wait.until(EC.presence_of_element_located(PASSWORD_SELECTOR))
        
        print("2. Đã tìm thấy các ô input. Bắt đầu điền thông tin...")
        natural_typing(username_field, USERNAME)
        time.sleep(random.uniform(0.5, 1))
        natural_typing(password_field, PASSWORD)

        # 3. Mô phỏng hành vi người dùng (ví dụ: cuộn trang) để tăng điểm tin cậy
        driver.execute_script("window.scrollTo(0, document.body.scrollHeight / 4);")
        time.sleep(random.uniform(1, 2.5))
        print("3. Đã mô phỏng hành vi người dùng.")

        # 4. Chờ và lấy token reCAPTCHA từ input ẩn
        # Thẻ này thường có name hoặc id là 'g-recaptcha-response'
        print("4. Đang chờ để lấy reCAPTCHA token...")
        recaptcha_token_element = wait.until(
            EC.presence_of_element_located((By.ID, "g-recaptcha-response"))
        )
        recaptcha_token = recaptcha_token_element.get_attribute("value")
        
        if not recaptcha_token:
            raise Exception("Không thể tìm thấy reCAPTCHA token. Script có thể đã bị phát hiện.")
        
        print(f"   -> Lấy token thành công! (Token bắt đầu bằng: {recaptcha_token[:30]}...)")
        
        # 5. Click nút đăng nhập
        login_button = wait.until(EC.element_to_be_clickable(LOGIN_BUTTON_SELECTOR))
        login_button.click()
        print("5. Đã nhấn nút đăng nhập.")
        
        # 6. Xác nhận đăng nhập thành công
        print("6. Đang chờ xác nhận đăng nhập thành công...")
        wait.until(EC.presence_of_element_located(SUCCESS_ELEMENT_SELECTOR))
        
        print("\n======================================")
        print("✅ ĐĂNG NHẬP THÀNH CÔNG!")
        print(f"URL hiện tại: {driver.current_url}")
        print("======================================")

        # Bạn có thể tiếp tục các tác vụ khác ở đây
        time.sleep(10) # Giữ trình duyệt 10 giây để xem kết quả

    except Exception as e:
        print("\n======================================")
        print(f"❌ ĐÃ XẢY RA LỖI: {e}")
        print("======================================")
        # Chụp ảnh màn hình để dễ dàng gỡ lỗi
        screenshot_path = "error_screenshot.png"
        driver.save_screenshot(screenshot_path)
        print(f"Đã lưu ảnh chụp màn hình lỗi tại: {screenshot_path}")

    finally:
        print(">>> Đóng trình duyệt.")
        driver.quit()

if __name__ == "__main__":
    main()