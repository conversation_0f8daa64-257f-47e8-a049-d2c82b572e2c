#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Minimal API để test Flask hoạt động
"""

from flask import Flask, jsonify, request, send_file
import json
import os
import base64
import requests
from datetime import datetime

app = Flask(__name__)

@app.route('/', methods=['GET'])
def home():
    return jsonify({
        'message': 'FPT eInvoice API Server',
        'version': '1.0.0',
        'endpoints': [
            'GET /',
            'GET /health',
            'GET /token-info',
            'POST /create-sample-invoice',
            'GET /view-invoice/<filename>'
        ]
    })

@app.route('/health', methods=['GET'])
def health():
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.now().isoformat(),
        'version': '1.0.0'
    })

@app.route('/token-info', methods=['GET'])
def token_info():
    try:
        if os.path.exists('session_token.json'):
            with open('session_token.json', 'r', encoding='utf-8') as f:
                token_data = json.load(f)
            
            return jsonify({
                'success': True,
                'has_token': bool(token_data.get('token')),
                'token_length': len(token_data.get('token', '')),
                'token_type': token_data.get('token_type'),
                'token_source': token_data.get('token_source'),
                'extracted_at': token_data.get('extracted_at')
            })
        else:
            return jsonify({
                'success': False,
                'error': 'Token file not found'
            })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/create-sample-invoice', methods=['POST'])
def create_sample_invoice():
    try:
        # Validate request
        if not request.is_json:
            return jsonify({
                'success': False,
                'error': 'Content-Type must be application/json'
            }), 400
        
        invoice_data = request.get_json()
        if not invoice_data:
            return jsonify({
                'success': False,
                'error': 'Request body cannot be empty'
            }), 400
        
        # Load token
        if not os.path.exists('session_token.json'):
            return jsonify({
                'success': False,
                'error': 'Token file not found. Please login first.'
            }), 401
        
        with open('session_token.json', 'r', encoding='utf-8') as f:
            token_data = json.load(f)
        
        token = token_data.get('token')
        if not token:
            return jsonify({
                'success': False,
                'error': 'No token found in file'
            }), 401
        
        # Call FPT API
        url = 'https://portal.einvoice.fpt.com.vn/api/inv/pdf/view'
        headers = {
            'Authorization': f'Bearer {token}',
            'Content-Type': 'application/json'
        }
        
        print(f"Calling FPT API: {url}")
        print(f"Token length: {len(token)}")
        
        response = requests.post(url, json=invoice_data, headers=headers, timeout=30)
        
        print(f"Response status: {response.status_code}")
        print(f"Response headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            # Create invoices directory
            os.makedirs('./invoices', exist_ok=True)
            
            # Generate filename
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"hdn_{timestamp}.pdf"
            filepath = os.path.join('./invoices', filename)
            
            # Save PDF
            pdf_data = None
            
            # Try different ways to get PDF data
            content_type = response.headers.get('content-type', '')
            
            if content_type.startswith('application/pdf'):
                # Binary PDF
                pdf_data = response.content
                print(f"Got binary PDF: {len(pdf_data)} bytes")
            else:
                # Try JSON response
                try:
                    response_json = response.json()
                    print(f"Response JSON keys: {list(response_json.keys())}")
                    
                    # Look for PDF data in common fields
                    for field in ['pdf', 'data', 'content', 'file', 'base64']:
                        if field in response_json:
                            pdf_data = response_json[field]
                            print(f"Found PDF data in field: {field}")
                            break
                    
                    if isinstance(pdf_data, str):
                        # Decode base64
                        if pdf_data.startswith('data:application/pdf;base64,'):
                            pdf_data = pdf_data[len('data:application/pdf;base64,'):]
                        
                        pdf_data = base64.b64decode(pdf_data)
                        print(f"Decoded base64 PDF: {len(pdf_data)} bytes")
                        
                except Exception as e:
                    print(f"Error parsing JSON: {e}")
                    return jsonify({
                        'success': False,
                        'error': f'Cannot parse response: {e}'
                    }), 500
            
            if pdf_data:
                # Save file
                with open(filepath, 'wb') as f:
                    f.write(pdf_data)
                
                print(f"Saved PDF: {filepath}")
                
                return jsonify({
                    'success': True,
                    'filename': filename,
                    'pdf_url': f'/view-invoice/{filename}',
                    'file_size': len(pdf_data),
                    'created_at': datetime.now().isoformat()
                })
            else:
                return jsonify({
                    'success': False,
                    'error': 'No PDF data found in response'
                }), 500
        
        else:
            return jsonify({
                'success': False,
                'error': f'FPT API error: {response.status_code} - {response.text}'
            }), 500
            
    except Exception as e:
        print(f"Error: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/view-invoice/<filename>', methods=['GET'])
def view_invoice(filename):
    try:
        filepath = os.path.join('./invoices', filename)
        
        if not os.path.exists(filepath):
            return jsonify({
                'success': False,
                'error': 'File not found'
            }), 404
        
        return send_file(filepath, mimetype='application/pdf')
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

if __name__ == '__main__':
    print("🚀 Starting FPT eInvoice API Server (Minimal Version)")
    print("📍 Server: http://localhost:5959")
    print("🔍 Endpoints:")
    print("   - GET  /")
    print("   - GET  /health")
    print("   - GET  /token-info")
    print("   - POST /create-sample-invoice")
    print("   - GET  /view-invoice/<filename>")
    print("="*50)
    
    app.run(host='0.0.0.0', port=5959, debug=True)
