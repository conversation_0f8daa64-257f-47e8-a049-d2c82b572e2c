# -*- coding: utf-8 -*-
"""
KỊCH BẢN TỰ ĐỘNG ĐĂNG NHẬP VỚI SELENIUM VÀ RECAPTCHA V3
---------------------------------------------------------
Y<PERSON>u cầu:
pip install selenium webdriver-manager selenium-stealth

Lưu ý:
- <PERSON><PERSON> thuật này phức tạp và có thể không ổn định nếu trang web thay đổi.
- Đ<PERSON> có độ tin cậy cao nhất trong môi trường production, hãy cân nhắc dùng dịch vụ giải CAPTCHA.
"""

import time
import random
import logging
import json
import os
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.service import Service as ChromeService
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException, WebDriverException
from webdriver_manager.chrome import ChromeDriverManager
from selenium_stealth import stealth

# Thiết lập logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# =====================================================================================
# <<< THAY ĐỔI CÁC GIÁ TRỊ NÀY CHO PHÙ HỢP VỚI TRANG WEB CỦA BẠN >>>
# =====================================================================================
LOGIN_URL = "https://portal.einvoice.fpt.com.vn/"  # Thay bằng URL trang đăng nhập thực tế
MASOTHUE = "0106512255"
USERNAME = "hhuong"             # Thay bằng username của bạn
PASSWORD = "eb6e8a09"                 # Thay bằng password của bạn

# --- Cấu hình Selector (quan trọng nhất!) ---
# Sử dụng By.ID, By.NAME, By.XPATH, hoặc By.CLASS_NAME...
# Ví dụ: (By.ID, "user-name"), (By.NAME, "password"), (By.XPATH, "//button[@type='submit']")
# Thứ tự điền: Mã số thuế -> Username -> Password -> Click đăng nhập
MASOTHUE_SELECTOR = (By.XPATH, "/html/body/div[3]/div[2]/div[2]/div[2]/div/div[3]/div/input")  # Ô mã số thuế
USERNAME_SELECTOR = (By.XPATH, "/html/body/div[3]/div[2]/div[2]/div[2]/div/div[4]/div/input")  # Ô tên đăng nhập
PASSWORD_SELECTOR = (By.XPATH, "/html/body/div[3]/div[2]/div[2]/div[2]/div/div[5]/div/input")  # Ô mật khẩu
LOGIN_BUTTON_SELECTOR = (By.XPATH, "/html/body/div[3]/div[2]/div[2]/div[2]/div/div[6]/div[1]/div/button")  # Nút đăng nhập

# --- Selector để xác nhận đăng nhập thành công ---
# Tìm một element chỉ xuất hiện SAU KHI đăng nhập thành công (ví dụ: avatar, dashboard...)
SUCCESS_ELEMENT_SELECTOR = (By.CLASS_NAME, "webix_disabled_view")
# =====================================================================================


def natural_typing(element, text: str):
    """Mô phỏng hành vi gõ phím tự nhiên của người dùng."""
    try:
        element.clear()  # Xóa nội dung cũ trước khi gõ
        for char in text:
            element.send_keys(char)
            time.sleep(random.uniform(0.05, 0.15))
    except Exception as e:
        logger.error(f"Lỗi khi gõ text '{text}': {e}")
        raise

def wait_for_recaptcha_v3(driver, wait, max_attempts=5):
    """
    Chờ reCAPTCHA v3 load và lấy token.
    reCAPTCHA v3 hoạt động khác v2, không có checkbox mà chạy ngầm.
    """
    logger.info("Đang chờ reCAPTCHA v3 khởi tạo...")

    for attempt in range(max_attempts):
        try:
            logger.info(f"Lần thử {attempt + 1}/{max_attempts}")

            # Chờ script reCAPTCHA load
            time.sleep(random.uniform(2, 4))

            # Kiểm tra xem reCAPTCHA script đã load chưa
            recaptcha_script_loaded = driver.execute_script("""
                return typeof grecaptcha !== 'undefined' &&
                       typeof grecaptcha.ready !== 'undefined';
            """)

            if not recaptcha_script_loaded:
                logger.warning(f"reCAPTCHA script chưa load (attempt {attempt + 1})")
                time.sleep(2)
                continue

            # Trigger reCAPTCHA v3 execution
            logger.info("Triggering reCAPTCHA v3...")
            driver.execute_script("""
                if (typeof grecaptcha !== 'undefined' && grecaptcha.ready) {
                    grecaptcha.ready(function() {
                        console.log('reCAPTCHA ready');
                    });
                }
            """)

            # Chờ một chút để reCAPTCHA xử lý
            time.sleep(random.uniform(3, 5))

            # Thử tìm token trong các element có thể
            token_selectors = [
                (By.ID, "g-recaptcha-response"),
                (By.NAME, "g-recaptcha-response"),
                (By.CSS_SELECTOR, "[name='g-recaptcha-response']"),
                (By.CSS_SELECTOR, "textarea[name='g-recaptcha-response']"),
                (By.XPATH, "//textarea[@name='g-recaptcha-response']")
            ]

            for selector in token_selectors:
                try:
                    token_element = driver.find_element(*selector)
                    token_value = token_element.get_attribute("value")

                    if token_value and len(token_value) > 10:
                        logger.info(f"✅ Tìm thấy reCAPTCHA token! (Length: {len(token_value)})")
                        return token_value
                    else:
                        logger.debug(f"Token element tìm thấy nhưng chưa có giá trị: {selector}")

                except NoSuchElementException:
                    logger.debug(f"Không tìm thấy element với selector: {selector}")
                    continue

            logger.warning(f"Không tìm thấy token hợp lệ (attempt {attempt + 1})")

            # Thử click vào trang để trigger reCAPTCHA
            if attempt < max_attempts - 1:
                logger.info("Thử click vào trang để trigger reCAPTCHA...")
                driver.execute_script("document.body.click();")
                time.sleep(2)

        except Exception as e:
            logger.error(f"Lỗi trong attempt {attempt + 1}: {e}")
            if attempt < max_attempts - 1:
                time.sleep(2)
                continue
            else:
                raise

    logger.warning("Không thể lấy reCAPTCHA token sau tất cả attempts")
    return None

def safe_find_element(driver, wait, selector, timeout=10, element_name="element"):
    """Tìm element một cách an toàn với error handling."""
    try:
        logger.info(f"Đang tìm {element_name}...")
        element = wait.until(EC.presence_of_element_located(selector))
        logger.info(f"✅ Tìm thấy {element_name}")
        return element
    except TimeoutException:
        logger.error(f"❌ Timeout khi tìm {element_name} sau {timeout}s")
        # Thử tìm bằng cách khác
        try:
            element = driver.find_element(*selector)
            logger.info(f"✅ Tìm thấy {element_name} bằng find_element")
            return element
        except NoSuchElementException:
            logger.error(f"❌ Không tìm thấy {element_name} bằng bất kỳ cách nào")
            raise
    except Exception as e:
        logger.error(f"❌ Lỗi không mong muốn khi tìm {element_name}: {e}")
        raise

def safe_click_element(driver, wait, selector, timeout=10, element_name="element"):
    """Click element một cách an toàn với error handling."""
    try:
        logger.info(f"Đang chờ {element_name} có thể click...")
        element = wait.until(EC.element_to_be_clickable(selector))

        # Scroll đến element trước khi click
        driver.execute_script("arguments[0].scrollIntoView(true);", element)
        time.sleep(0.5)

        # Thử click bằng Selenium
        try:
            element.click()
            logger.info(f"✅ Đã click {element_name} bằng Selenium")
            return True
        except Exception as e:
            logger.warning(f"Click Selenium thất bại: {e}, thử JavaScript...")
            # Fallback: click bằng JavaScript
            driver.execute_script("arguments[0].click();", element)
            logger.info(f"✅ Đã click {element_name} bằng JavaScript")
            return True

    except TimeoutException:
        logger.error(f"❌ Timeout khi chờ {element_name} có thể click sau {timeout}s")
        return False
    except Exception as e:
        logger.error(f"❌ Lỗi khi click {element_name}: {e}")
        return False

def extract_session_token(driver):
    """
    Trích xuất session token từ browser storage.
    Tìm kiếm trong sessionStorage, localStorage và cookies.
    """
    logger.info("🔍 Đang trích xuất session token...")

    try:
        # 1. Kiểm tra sessionStorage
        logger.info("   -> Kiểm tra sessionStorage...")
        session_storage_script = """
            var sessionData = {};
            for (var i = 0; i < sessionStorage.length; i++) {
                var key = sessionStorage.key(i);
                var value = sessionStorage.getItem(key);
                sessionData[key] = value;
            }
            return sessionData;
        """

        session_storage = driver.execute_script(session_storage_script)
        logger.info(f"   -> SessionStorage keys: {list(session_storage.keys())}")

        # Tìm token trong sessionStorage
        token_found = None
        token_source = None

        # Kiểm tra key 'Session'
        if 'Session' in session_storage:
            try:
                session_value = session_storage['Session']
                logger.info(f"   -> Tìm thấy Session key, value length: {len(session_value)}")

                # Thử parse JSON
                session_json = json.loads(session_value)
                logger.info(f"   -> Session JSON keys: {list(session_json.keys())}")

                # Tìm token trong JSON
                possible_token_keys = ['token', 'access_token', 'accessToken', 'authToken', 'jwt', 'sessionToken']
                for key in possible_token_keys:
                    if key in session_json and session_json[key]:
                        token_found = session_json[key]
                        token_source = f"sessionStorage.Session.{key}"
                        logger.info(f"   -> ✅ Tìm thấy token trong {token_source}")
                        break

            except json.JSONDecodeError as e:
                logger.warning(f"   -> Session value không phải JSON: {e}")
                # Có thể Session value chính là token
                if len(session_storage['Session']) > 50:  # Token thường dài
                    token_found = session_storage['Session']
                    token_source = "sessionStorage.Session"
                    logger.info(f"   -> ✅ Session value có thể là token (length: {len(token_found)})")

        # Nếu chưa tìm thấy, kiểm tra các key khác trong sessionStorage
        if not token_found:
            logger.info("   -> Tìm kiếm token trong các key khác của sessionStorage...")
            for key, value in session_storage.items():
                if any(keyword in key.lower() for keyword in ['token', 'auth', 'session', 'jwt']):
                    if value and len(value) > 50:  # Token thường dài
                        token_found = value
                        token_source = f"sessionStorage.{key}"
                        logger.info(f"   -> ✅ Tìm thấy token trong {token_source}")
                        break

        # 2. Kiểm tra localStorage nếu chưa tìm thấy
        if not token_found:
            logger.info("   -> Kiểm tra localStorage...")
            local_storage_script = """
                var localData = {};
                for (var i = 0; i < localStorage.length; i++) {
                    var key = localStorage.key(i);
                    var value = localStorage.getItem(key);
                    localData[key] = value;
                }
                return localData;
            """

            local_storage = driver.execute_script(local_storage_script)
            logger.info(f"   -> LocalStorage keys: {list(local_storage.keys())}")

            for key, value in local_storage.items():
                if any(keyword in key.lower() for keyword in ['token', 'auth', 'session', 'jwt']):
                    if value and len(value) > 50:
                        token_found = value
                        token_source = f"localStorage.{key}"
                        logger.info(f"   -> ✅ Tìm thấy token trong {token_source}")
                        break

        # 3. Kiểm tra cookies nếu vẫn chưa tìm thấy
        if not token_found:
            logger.info("   -> Kiểm tra cookies...")
            cookies = driver.get_cookies()
            logger.info(f"   -> Cookies count: {len(cookies)}")

            for cookie in cookies:
                cookie_name = cookie.get('name', '').lower()
                cookie_value = cookie.get('value', '')

                if any(keyword in cookie_name for keyword in ['token', 'auth', 'session', 'jwt']):
                    if cookie_value and len(cookie_value) > 50:
                        token_found = cookie_value
                        token_source = f"cookie.{cookie['name']}"
                        logger.info(f"   -> ✅ Tìm thấy token trong {token_source}")
                        break

        # 4. Thử truy cập trực tiếp các biến JavaScript global
        if not token_found:
            logger.info("   -> Kiểm tra biến JavaScript global...")
            js_vars_script = """
                var result = {};

                // Kiểm tra các biến global phổ biến
                var possibleVars = ['token', 'authToken', 'accessToken', 'sessionToken', 'userToken', 'jwt'];

                for (var i = 0; i < possibleVars.length; i++) {
                    var varName = possibleVars[i];
                    if (typeof window[varName] !== 'undefined' && window[varName]) {
                        result[varName] = window[varName];
                    }
                }

                // Kiểm tra trong window.app hoặc window.config
                if (typeof window.app !== 'undefined' && window.app) {
                    result.app = window.app;
                }
                if (typeof window.config !== 'undefined' && window.config) {
                    result.config = window.config;
                }

                return result;
            """

            js_vars = driver.execute_script(js_vars_script)
            logger.info(f"   -> JavaScript vars: {list(js_vars.keys())}")

            for key, value in js_vars.items():
                if isinstance(value, str) and len(value) > 50:
                    token_found = value
                    token_source = f"window.{key}"
                    logger.info(f"   -> ✅ Tìm thấy token trong {token_source}")
                    break
                elif isinstance(value, dict):
                    # Tìm trong object
                    for sub_key, sub_value in value.items():
                        if any(keyword in sub_key.lower() for keyword in ['token', 'auth', 'jwt']):
                            if isinstance(sub_value, str) and len(sub_value) > 50:
                                token_found = sub_value
                                token_source = f"window.{key}.{sub_key}"
                                logger.info(f"   -> ✅ Tìm thấy token trong {token_source}")
                                break
                    if token_found:
                        break

        if token_found:
            logger.info(f"🎉 Trích xuất token thành công từ {token_source}")
            logger.info(f"   -> Token length: {len(token_found)}")
            logger.info(f"   -> Token preview: {token_found[:50]}...")
            return token_found, token_source
        else:
            logger.warning("⚠️ Không tìm thấy token trong bất kỳ storage nào")
            return None, None

    except Exception as e:
        logger.error(f"❌ Lỗi khi trích xuất token: {e}")
        return None, None

def save_session_token(token, token_source, filename="session_token.json"):
    """
    Lưu session token vào file JSON.
    """
    try:
        logger.info(f"💾 Đang lưu token vào file {filename}...")

        # Tạo data structure
        token_data = {
            "token": token,
            "token_source": token_source,
            "extracted_at": datetime.now().isoformat(),
            "origin": "https://portal.einvoice.fpt.com.vn",
            "token_length": len(token),
            "token_preview": token[:50] + "..." if len(token) > 50 else token
        }

        # Lưu vào file
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(token_data, f, indent=2, ensure_ascii=False)

        logger.info(f"✅ Đã lưu token thành công vào {filename}")
        logger.info(f"   -> File size: {os.path.getsize(filename)} bytes")

        return True

    except Exception as e:
        logger.error(f"❌ Lỗi khi lưu token: {e}")
        return False

def validate_token(token):
    """
    Validate token format và content.
    """
    if not token:
        return False, "Token rỗng"

    if len(token) < 20:
        return False, "Token quá ngắn"

    # Kiểm tra JWT format (có 3 phần phân cách bởi dấu chấm)
    if token.count('.') == 2:
        return True, "JWT token hợp lệ"

    # Kiểm tra các format khác
    if len(token) > 50 and token.isalnum():
        return True, "Token alphanumeric hợp lệ"

    if len(token) > 50:
        return True, "Token có vẻ hợp lệ"

    return False, "Token không đúng format"

def setup_chrome_driver():
    """Thiết lập Chrome driver với các options tối ưu cho stability."""
    logger.info("Đang thiết lập Chrome driver...")

    options = webdriver.ChromeOptions()

    # Basic options
    options.add_argument("--start-maximized")
    options.add_argument("--no-sandbox")
    options.add_argument("--disable-dev-shm-usage")
    options.add_argument("--disable-gpu")
    options.add_argument("--disable-web-security")
    options.add_argument("--allow-running-insecure-content")

    # Disable automation detection
    options.add_experimental_option("excludeSwitches", ["enable-automation"])
    options.add_experimental_option('useAutomationExtension', False)
    options.add_argument("--disable-blink-features=AutomationControlled")

    # Disable logging để giảm TensorFlow warnings
    options.add_argument("--disable-logging")
    options.add_argument("--log-level=3")
    options.add_argument("--silent")
    options.add_experimental_option('excludeSwitches', ['enable-logging'])
    options.add_experimental_option('useAutomationExtension', False)

    # Performance improvements
    options.add_argument("--disable-extensions")
    options.add_argument("--disable-plugins")
    options.add_argument("--disable-images")
    options.add_argument("--disable-javascript-harmony-shipping")

    # User agent
    options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")

    # Prefs để disable notifications và các popup
    prefs = {
        "profile.default_content_setting_values": {
            "notifications": 2,
            "media_stream": 2,
        },
        "profile.managed_default_content_settings": {
            "images": 2
        }
    }
    options.add_experimental_option("prefs", prefs)

    try:
        service = ChromeService(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=options)
        logger.info("✅ Chrome driver đã được thiết lập thành công")
        return driver
    except Exception as e:
        logger.error(f"❌ Lỗi khi thiết lập Chrome driver: {e}")
        raise

def main():
    """Hàm chính thực thi toàn bộ luồng đăng nhập."""
    driver = None

    try:
        # Thiết lập driver
        driver = setup_chrome_driver()

        # Áp dụng "tàng hình" để tránh bị phát hiện
        logger.info("Áp dụng stealth mode...")
        stealth(driver,
                languages=["en-US", "en"],
                vendor="Google Inc.",
                platform="Win32",
                webgl_vendor="Intel Inc.",
                renderer="Intel Iris OpenGL Engine",
                fix_hairline=True,
                )

        logger.info(">>> Bắt đầu quá trình đăng nhập tự động...")
        driver.get(LOGIN_URL)
        wait = WebDriverWait(driver, 30)  # Tăng timeout lên 30s

        logger.info(f"1. Đã truy cập trang: {LOGIN_URL}")
        time.sleep(random.uniform(3, 5))  # Chờ lâu hơn để trang load hoàn toàn

        # 2. Tìm và điền thông tin đăng nhập với error handling
        logger.info("2. Đang tìm các ô input...")

        masothue_field = safe_find_element(driver, wait, MASOTHUE_SELECTOR,
                                         element_name="ô mã số thuế")
        username_field = safe_find_element(driver, wait, USERNAME_SELECTOR,
                                         element_name="ô username")
        password_field = safe_find_element(driver, wait, PASSWORD_SELECTOR,
                                         element_name="ô password")

        logger.info("✅ Đã tìm thấy tất cả ô input. Bắt đầu điền thông tin...")

        # Điền thông tin với error handling
        logger.info(f"   -> Điền mã số thuế: {MASOTHUE}")
        natural_typing(masothue_field, MASOTHUE)
        time.sleep(random.uniform(0.5, 1))

        logger.info(f"   -> Điền username: {USERNAME}")
        natural_typing(username_field, USERNAME)
        time.sleep(random.uniform(0.5, 1))

        logger.info("   -> Điền password: ********")
        natural_typing(password_field, PASSWORD)

        # 3. Mô phỏng hành vi người dùng
        logger.info("3. Mô phỏng hành vi người dùng...")
        driver.execute_script("window.scrollTo(0, document.body.scrollHeight / 4);")
        time.sleep(random.uniform(1, 2))

        # Di chuyển chuột ngẫu nhiên
        driver.execute_script("""
            var event = new MouseEvent('mousemove', {
                'view': window,
                'bubbles': true,
                'cancelable': true,
                'clientX': Math.random() * window.innerWidth,
                'clientY': Math.random() * window.innerHeight
            });
            document.dispatchEvent(event);
        """)
        time.sleep(random.uniform(1, 2))

        # 4. Xử lý reCAPTCHA v3 với error handling cải thiện
        logger.info("4. Đang xử lý reCAPTCHA v3...")
        recaptcha_token = wait_for_recaptcha_v3(driver, wait, max_attempts=3)

        if recaptcha_token:
            logger.info(f"✅ Lấy reCAPTCHA token thành công! (Length: {len(recaptcha_token)})")
        else:
            logger.warning("⚠️ Không lấy được reCAPTCHA token, tiếp tục thử đăng nhập...")

        # 5. Click nút đăng nhập với error handling
        logger.info("5. Đang click nút đăng nhập...")
        click_success = safe_click_element(driver, wait, LOGIN_BUTTON_SELECTOR,
                                         element_name="nút đăng nhập")

        if not click_success:
            raise Exception("Không thể click nút đăng nhập")

        logger.info("✅ Đã click nút đăng nhập")

        # 6. Chờ và xác nhận kết quả
        logger.info("6. Đang chờ kết quả đăng nhập...")
        time.sleep(random.uniform(3, 5))

        try:
            # Thử tìm element thành công
            success_element = wait.until(EC.presence_of_element_located(SUCCESS_ELEMENT_SELECTOR))

            logger.info("\n" + "="*50)
            logger.info("✅ ĐĂNG NHẬP THÀNH CÔNG!")
            logger.info(f"URL hiện tại: {driver.current_url}")
            logger.info("="*50)

            # Chờ một chút để trang load hoàn toàn và session được thiết lập
            logger.info("⏳ Chờ session được thiết lập...")
            time.sleep(random.uniform(3, 5))

            # Trích xuất session token
            token, token_source = extract_session_token(driver)

            if token:
                # Validate token
                is_valid, validation_msg = validate_token(token)
                logger.info(f"🔍 Token validation: {validation_msg}")

                if is_valid:
                    # Lưu token vào file
                    save_success = save_session_token(token, token_source)

                    if save_success:
                        logger.info("🎉 HOÀN THÀNH! Token đã được trích xuất và lưu thành công!")

                        # Hiển thị thông tin token
                        logger.info("\n" + "="*50)
                        logger.info("📋 THÔNG TIN TOKEN:")
                        logger.info(f"   🔗 Source: {token_source}")
                        logger.info(f"   📏 Length: {len(token)} characters")
                        logger.info(f"   🔍 Preview: {token[:50]}...")
                        logger.info(f"   💾 Saved to: session_token.json")
                        logger.info("="*50)
                    else:
                        logger.error("❌ Không thể lưu token vào file")
                else:
                    logger.warning(f"⚠️ Token không hợp lệ: {validation_msg}")
            else:
                logger.warning("⚠️ Không thể trích xuất token từ browser storage")

                # Debug: Hiển thị tất cả storage data
                logger.info("🔍 Debug: Hiển thị tất cả storage data...")
                debug_script = """
                    var debug = {
                        sessionStorage: {},
                        localStorage: {},
                        cookies: document.cookie,
                        url: window.location.href,
                        title: document.title
                    };

                    // SessionStorage
                    for (var i = 0; i < sessionStorage.length; i++) {
                        var key = sessionStorage.key(i);
                        debug.sessionStorage[key] = sessionStorage.getItem(key).substring(0, 100) + '...';
                    }

                    // LocalStorage
                    for (var i = 0; i < localStorage.length; i++) {
                        var key = localStorage.key(i);
                        debug.localStorage[key] = localStorage.getItem(key).substring(0, 100) + '...';
                    }

                    return debug;
                """

                debug_data = driver.execute_script(debug_script)
                logger.info(f"Debug data: {json.dumps(debug_data, indent=2)}")

            # Giữ trình duyệt để xem kết quả
            logger.info("⏳ Giữ trình duyệt 15 giây để kiểm tra...")
            time.sleep(15)

        except TimeoutException:
            # Kiểm tra xem có lỗi đăng nhập không
            current_url = driver.current_url
            page_source = driver.page_source

            logger.warning(f"Không tìm thấy element thành công. URL hiện tại: {current_url}")

            # Kiểm tra các dấu hiệu lỗi
            error_indicators = [
                "error", "invalid", "incorrect", "failed", "sai", "lỗi"
            ]

            page_text = page_source.lower()
            found_errors = [indicator for indicator in error_indicators if indicator in page_text]

            if found_errors:
                logger.error(f"❌ Phát hiện lỗi đăng nhập: {found_errors}")
            else:
                logger.info("⚠️ Không rõ trạng thái đăng nhập, có thể cần kiểm tra thủ công")

            # Chụp ảnh để debug
            screenshot_path = f"login_result_{int(time.time())}.png"
            driver.save_screenshot(screenshot_path)
            logger.info(f"Đã lưu screenshot: {screenshot_path}")

    except WebDriverException as e:
        logger.error(f"❌ Lỗi WebDriver: {e}")
        if driver:
            try:
                screenshot_path = f"webdriver_error_{int(time.time())}.png"
                driver.save_screenshot(screenshot_path)
                logger.info(f"Đã lưu screenshot lỗi: {screenshot_path}")
            except:
                pass
    except TimeoutException as e:
        logger.error(f"❌ Timeout: {e}")
        if driver:
            try:
                screenshot_path = f"timeout_error_{int(time.time())}.png"
                driver.save_screenshot(screenshot_path)
                logger.info(f"Đã lưu screenshot timeout: {screenshot_path}")
            except:
                pass
    except Exception as e:
        logger.error(f"❌ Lỗi không mong muốn: {e}")
        if driver:
            try:
                screenshot_path = f"general_error_{int(time.time())}.png"
                driver.save_screenshot(screenshot_path)
                logger.info(f"Đã lưu screenshot lỗi: {screenshot_path}")
            except:
                pass
    finally:
        if driver:
            logger.info(">>> Đóng trình duyệt...")
            try:
                driver.quit()
            except:
                pass
        logger.info(">>> Hoàn thành!")

if __name__ == "__main__":
    try:
        logger.info("🚀 Khởi động FPT eInvoice Selenium Automation")
        logger.info("="*60)
        main()
    except KeyboardInterrupt:
        logger.info("❌ Người dùng dừng chương trình")
    except Exception as e:
        logger.error(f"❌ Lỗi nghiêm trọng: {e}")
    finally:
        logger.info("🏁 Kết thúc chương trình")