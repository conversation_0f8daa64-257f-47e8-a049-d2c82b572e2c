# Auto Re-Authentication Guide

## 🎯 Tổng quan

FPT eInvoice API Server được trang bị tính năng **Auto Re-Authentication** thông minh, tự động xử lý token hết hạn mà không cần can thiệ<PERSON> thủ công.

## 🔄 Workflow Chi tiết

### 1. <PERSON><PERSON><PERSON> hiện Token Hết hạn
```python
def is_token_expired_error(status_code, response_text):
    # Ph<PERSON><PERSON> hiện các dấu hiệu token hết hạn:
    # - HTTP 401 (Unauthorized)
    # - HTTP 500 với error message chứa "authentication"
    # - <PERSON><PERSON><PERSON> từ khóa: "invalid_authentication_information", "expired", "token"
```

### 2. Tự động Re-Authentication
```python
def run_selenium_authentication():
    # Chạy subprocess: python selenium_automation.py
    # Timeout: 120 giây
    # Capture output để logging
    # Kiểm tra token mới được tạo
```

### 3. Retry Logic
```python
def call_fpt_api_with_retry(invoice_data, max_retries=1):
    # Thử gọi API với token hiện tại
    # Nếu token expired → chạy re-auth → retry
    # Tối đa 1 lần retry để tránh infinite loop
```

## 📊 Error Detection Matrix

| HTTP Status | Response Content | Action |
|-------------|------------------|---------|
| 200 | Success | ✅ Return PDF |
| 401 | Any | 🔄 Re-authenticate |
| 500 | Contains "authentication" | 🔄 Re-authenticate |
| 500 | Other errors | ❌ Return error |
| 4xx/5xx | Network/Server errors | ❌ Return error |

## 🛠️ Implementation Details

### Endpoints với Auto Re-Auth:
- ✅ `POST /create-sample-invoice`
- ✅ `POST /create-view-sample-invoice`

### Logging Levels:
```python
logger.info("🔄 FPT API attempt 1/2")
logger.warning("⚠️ Token expired error detected")
logger.info("🔄 Attempting re-authentication...")
logger.info("✅ Re-authentication thành công")
logger.error("❌ Re-authentication thất bại")
```

### Timeout Configuration:
- **API Call**: 30 seconds
- **Selenium Script**: 120 seconds  
- **Total Max Time**: ~150 seconds per request

## 🔧 Configuration

### Environment Variables (.env):
```env
FPT_MASOTHUE=your_tax_code
FPT_USERNAME=your_username  
FPT_PASSWORD=your_password
```

### Retry Settings (api_server.py):
```python
# Số lần retry tối đa
max_retries = 1

# Timeout cho selenium
timeout = 120

# Delay giữa các retry
time.sleep(2)
```

## 📈 Performance Impact

### Normal Operation:
- **Response Time**: ~2-5 seconds
- **Success Rate**: 99%+

### With Re-Authentication:
- **Response Time**: ~60-120 seconds (first time)
- **Subsequent Calls**: ~2-5 seconds (with new token)
- **Success Rate**: 95%+ (depends on network/selenium)

## 🚨 Error Scenarios

### 1. Selenium Script Fails
```json
{
  "success": false,
  "error": "Re-authentication failed: Selenium failed: Chrome not found"
}
```

### 2. Selenium Timeout
```json
{
  "success": false,
  "error": "Re-authentication failed: Authentication timeout"
}
```

### 3. Invalid Credentials
```json
{
  "success": false,
  "error": "Re-authentication failed: New token is invalid"
}
```

### 4. Network Issues
```json
{
  "success": false,
  "error": "API call failed: Connection timeout"
}
```

## 🔍 Monitoring & Debugging

### Server Logs:
```bash
# Theo dõi logs real-time
tail -f api_server.log

# Hoặc xem console output
python api_server.py
```

### Key Log Messages:
```
INFO - 🔄 FPT API attempt 1/2
WARNING - ⚠️ Token expired error detected: 500
INFO - 🔄 Attempting re-authentication...
INFO - ✅ Selenium script hoàn thành thành công
INFO - ✅ Token mới đã được tạo (length: 3060)
INFO - ✅ Re-authentication thành công, retry API call...
INFO - ✅ FPT API thành công
```

### Manual Testing:
```bash
# Test với token hợp lệ
curl -X POST http://localhost:5959/create-sample-invoice \
  -H "Content-Type: application/json" \
  -d '{"test": "data"}'

# Simulate token expiry (thay đổi token trong session_token.json)
# Sau đó test lại để xem auto re-auth
```

## 🎯 Best Practices

### 1. Environment Setup:
- ✅ Đảm bảo Chrome browser được cài đặt
- ✅ Kiểm tra thông tin đăng nhập trong .env
- ✅ Test selenium script manual trước

### 2. Production Deployment:
- ✅ Sử dụng production WSGI server (gunicorn, uwsgi)
- ✅ Setup proper logging với log rotation
- ✅ Monitor server health và token expiry
- ✅ Backup token file định kỳ

### 3. Error Handling:
- ✅ Implement circuit breaker pattern
- ✅ Add metrics và alerting
- ✅ Setup fallback mechanisms
- ✅ Regular health checks

## 🔮 Future Enhancements

### Planned Features:
- 🔄 **Token Refresh Schedule** - Refresh token trước khi hết hạn
- 📊 **Metrics Dashboard** - Monitor success rate, response time
- 🔔 **Alerting System** - Notify khi re-auth fails
- 🏃 **Headless Mode** - Selenium chạy background
- 💾 **Token Caching** - Cache multiple tokens
- 🔐 **Token Encryption** - Encrypt token storage

### Performance Optimizations:
- ⚡ **Async Processing** - Non-blocking re-authentication
- 🚀 **Connection Pooling** - Reuse HTTP connections
- 📦 **Response Caching** - Cache PDF responses
- 🎯 **Smart Retry** - Exponential backoff

---

## 📞 Support

Nếu gặp vấn đề với Auto Re-Authentication:

1. **Check logs** - Xem chi tiết lỗi trong console/log file
2. **Test selenium** - Chạy `python selenium_automation.py` manual
3. **Verify credentials** - Kiểm tra thông tin trong .env
4. **Network check** - Đảm bảo kết nối internet ổn định
5. **Browser update** - Cập nhật Chrome browser

**Auto Re-Authentication giúp API hoạt động liên tục 24/7 mà không cần can thiệp thủ công!** 🚀
