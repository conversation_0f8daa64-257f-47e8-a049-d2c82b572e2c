# FPT eInvoice Integration Project

Hệ thống tích hợp với FPT eInvoice để tự động đăng nhập, trích xuất token và tạo hóa đơn PDF.

## 🚀 Tính năng chính

- ✅ **Đăng nhập tự động** vào FPT eInvoice portal bằng Selenium
- ✅ **Trích xuất JWT token** từ browser session storage
- ✅ **API Server** để tạo hóa đơn PDF từ JSON data
- ✅ **Xem PDF** trực tiếp trong browser
- ✅ **Quản lý token** tự động

## 📁 Cấu trúc project

```
├── selenium_automation.py    # Đăng nhập tự động và trích xuất token
├── api_server.py            # API server tạo hóa đơn PDF
├── pdf_viewer.py            # Hiển thị PDF từ response.json
├── session_token.json       # JWT token (tự động tạo)
├── sample_invoice.json      # Dữ liệu hóa đơn mẫu
├── requirements.txt         # Python dependencies
├── invoices/               # Thư mục lưu PDF (tự động tạo)
├── sample/                 # Dữ liệu mẫu từ FPT
└── README.md               # Tài liệu này
```

## 🔧 Cài đặt

### 1. Cài đặt dependencies

```bash
pip install -r requirements.txt
```

### 2. Cấu hình thông tin đăng nhập

Tạo file `.env` từ template:

```bash
cp .env.example .env
```

Chỉnh sửa file `.env` với thông tin thực tế:

```env
# Authentication Credentials
FPT_MASOTHUE=your_tax_code_here      # Mã số thuế
FPT_USERNAME=your_username_here      # Tên đăng nhập
FPT_PASSWORD=your_password_here      # Mật khẩu
```

**⚠️ Lưu ý:** File `.env` chứa thông tin nhạy cảm và đã được thêm vào `.gitignore`

## 📖 Cách sử dụng

### Bước 1: Đăng nhập và lấy token

```bash
python selenium_automation.py
```

**Kết quả:**
- Tự động đăng nhập vào FPT eInvoice portal
- Trích xuất JWT token từ session storage
- Lưu token vào file `session_token.json`

### Bước 2: Khởi động API server

```bash
python api_server.py
```

**Server chạy tại:** `http://localhost:5959`

### Bước 3: Tạo hóa đơn PDF

#### Cách 1: Lưu file và xem sau
```bash
curl -X POST http://localhost:5959/create-sample-invoice \
  -H "Content-Type: application/json" \
  -d @sample_invoice.json
```

#### Cách 2: Xem trực tiếp (Recommended)
```bash
curl -X POST http://localhost:5959/create-view-sample-invoice \
  -H "Content-Type: application/json" \
  -d @sample_invoice.json \
  --output invoice.pdf
```

#### Sử dụng Python:
```python
import requests
import json

with open('sample_invoice.json', 'r') as f:
    data = json.load(f)

# Cách 1: Lưu file
response = requests.post(
    'http://localhost:5959/create-sample-invoice',
    json=data
)
result = response.json()
print(f"PDF URL: {result['pdf_url']}")

# Cách 2: Nhận PDF trực tiếp
response = requests.post(
    'http://localhost:5959/create-view-sample-invoice',
    json=data
)
with open('invoice.pdf', 'wb') as f:
    f.write(response.content)
```

### Bước 4: Xem PDF

- **Trực tiếp:** Gọi `/create-view-sample-invoice` và mở trong browser
- **Từ file:** Truy cập `http://localhost:5959/view-invoice/{filename}`

## 🌐 API Endpoints

### 1. Health Check
```http
GET /health
```

### 2. Token Info
```http
GET /token-info
```

### 3. Tạo hóa đơn (lưu file)
```http
POST /create-sample-invoice
Content-Type: application/json

{
  "buyer": {...},
  "seller": {...},
  "items": [...],
  "amounts": {...}
}
```

### 4. Tạo và xem hóa đơn trực tiếp
```http
POST /create-view-sample-invoice
Content-Type: application/json

{
  "buyer": {...},
  "seller": {...},
  "items": [...],
  "amounts": {...}
}
```
**Response:** PDF file stream (hiển thị trực tiếp trong browser)

### 5. Xem PDF đã lưu
```http
GET /view-invoice/{filename}
```

## 📋 Dữ liệu hóa đơn mẫu

File `sample_invoice.json` chứa cấu trúc dữ liệu hoàn chỉnh:

```json
{
  "buyer": {
    "name": "CÔNG TY TNHH ABC",
    "address": "123 Đường ABC, TP.HCM",
    "tax_code": "**********"
  },
  "seller": {
    "name": "CÔNG TY TNHH XYZ", 
    "address": "456 Đường XYZ, Hà Nội",
    "tax_code": "**********"
  },
  "items": [
    {
      "name": "Dịch vụ kho bãi",
      "quantity": 1,
      "unit_price": 5000000,
      "tax_rate": 10
    }
  ],
  "amounts": {
    "subtotal": 7500000,
    "tax_amount": 750000,
    "total": 8250000
  }
}
```

## 🔒 Bảo mật

- ✅ **JWT Token** được lưu trữ cục bộ
- ✅ **Thông tin đăng nhập** không được commit vào git
- ✅ **File validation** để tránh path traversal
- ✅ **Token expiry** được xử lý tự động

## 🐛 Troubleshooting

### Lỗi "Không thể load token"
```bash
# Chạy lại selenium để lấy token mới
python selenium_automation.py
```

### Lỗi "FPT API failed"
- Kiểm tra token có hết hạn không
- Kiểm tra kết nối internet
- Kiểm tra format dữ liệu JSON

### Lỗi Chrome driver
```bash
# Cập nhật Chrome driver
pip install --upgrade webdriver-manager
```

## 📊 Logging

Logs được ghi ra console với các level:
- `INFO`: Thông tin chung
- `WARNING`: Cảnh báo
- `ERROR`: Lỗi

## 🔄 Workflow hoàn chỉnh

1. **Đăng nhập** → `selenium_automation.py`
2. **Lấy token** → `session_token.json`
3. **Khởi động API** → `api_server.py`
4. **Tạo hóa đơn** → POST `/create-sample-invoice`
5. **Lưu PDF** → `./invoices/hdn_YYYYMMDD_HHMMSS.pdf`
6. **Xem PDF** → GET `/view-invoice/{filename}`

## 📞 Hỗ trợ

- 📧 Email: <EMAIL>
- 🌐 Website: https://einvoice.fpt.com.vn

---

**⚠️ Lưu ý:**
- Đây là môi trường UAT (User Acceptance Testing)
- Token có thời hạn, cần refresh định kỳ
- Không sử dụng dữ liệu thật trong môi trường test
