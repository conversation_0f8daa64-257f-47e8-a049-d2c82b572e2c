#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FPT eInvoice API Server
Tạo và xử lý hóa đơn điện tử thông qua FPT eInvoice API
"""

import os
import json
import base64
import logging
import requests
from datetime import datetime
from pathlib import Path
from flask import Flask, request, jsonify, send_file, abort
from werkzeug.exceptions import BadRequest
import traceback

# Thiết lập logging đơn giản
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Khởi tạo Flask app
app = Flask(__name__)
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size

# Cấu hình
CONFIG = {
    'FPT_API_BASE_URL': 'https://portal.einvoice.fpt.com.vn',
    'FPT_API_ENDPOINT': '/api/inv/pdf/view',
    'TOKEN_FILE': 'session_token.json',
    'INVOICE_DIR': './invoices',
    'REQUEST_TIMEOUT': 30,
    'MAX_RETRIES': 3
}

# Tạo thư mục lưu hóa đơn nếu chưa tồn tại
os.makedirs(CONFIG['INVOICE_DIR'], exist_ok=True)

def load_token():
    """
    Đọc JWT token từ file session_token.json
    """
    try:
        if not os.path.exists(CONFIG['TOKEN_FILE']):
            logger.error(f"Token file không tồn tại: {CONFIG['TOKEN_FILE']}")
            return None
            
        with open(CONFIG['TOKEN_FILE'], 'r', encoding='utf-8') as f:
            token_data = json.load(f)
            
        token = token_data.get('token')
        if not token:
            logger.error("Không tìm thấy token trong file")
            return None
            
        # Kiểm tra token có phải JWT không
        if token.count('.') != 2:
            logger.error("Token không đúng format JWT")
            return None
            
        logger.info(f"Đã load token thành công (length: {len(token)})")
        return token
        
    except Exception as e:
        logger.error(f"Lỗi khi đọc token: {e}")
        return None

def validate_invoice_data(data):
    """
    Validate dữ liệu hóa đơn đầu vào
    """
    required_fields = [
        'buyer', 'seller', 'items', 'amounts'
    ]
    
    for field in required_fields:
        if field not in data:
            return False, f"Thiếu trường bắt buộc: {field}"
    
    # Validate buyer info
    buyer_required = ['name', 'address']
    for field in buyer_required:
        if field not in data['buyer']:
            return False, f"Thiếu thông tin buyer.{field}"
    
    # Validate seller info
    seller_required = ['name', 'address', 'tax_code']
    for field in seller_required:
        if field not in data['seller']:
            return False, f"Thiếu thông tin seller.{field}"
    
    # Validate items
    if not isinstance(data['items'], list) or len(data['items']) == 0:
        return False, "items phải là array và không được rỗng"
    
    for i, item in enumerate(data['items']):
        item_required = ['name', 'quantity', 'unit_price']
        for field in item_required:
            if field not in item:
                return False, f"Thiếu thông tin items[{i}].{field}"
    
    # Validate amounts
    amounts_required = ['subtotal', 'tax_amount', 'total']
    for field in amounts_required:
        if field not in data['amounts']:
            return False, f"Thiếu thông tin amounts.{field}"
    
    return True, "Valid"

def call_fpt_api(invoice_data, token):
    """
    Gọi FPT eInvoice API để tạo PDF
    """
    try:
        url = CONFIG['FPT_API_BASE_URL'] + CONFIG['FPT_API_ENDPOINT']
        
        headers = {
            'Authorization': f'Bearer {token}',
            'Content-Type': 'application/json',
            'User-Agent': 'FPT-eInvoice-API-Client/1.0'
        }
        
        logger.info(f"Gọi FPT API: {url}")
        logger.info(f"Headers: {dict(headers)}")
        
        # Gọi API với retry logic
        last_exception = None
        for attempt in range(CONFIG['MAX_RETRIES']):
            try:
                logger.info(f"Attempt {attempt + 1}/{CONFIG['MAX_RETRIES']}")
                
                response = requests.post(
                    url,
                    json=invoice_data,
                    headers=headers,
                    timeout=CONFIG['REQUEST_TIMEOUT']
                )
                
                logger.info(f"Response status: {response.status_code}")
                logger.info(f"Response headers: {dict(response.headers)}")
                
                if response.status_code == 200:
                    return True, response
                elif response.status_code == 401:
                    return False, "Token không hợp lệ hoặc đã hết hạn"
                elif response.status_code == 403:
                    return False, "Không có quyền truy cập"
                elif response.status_code == 400:
                    return False, f"Dữ liệu không hợp lệ: {response.text}"
                else:
                    return False, f"API error: {response.status_code} - {response.text}"
                    
            except requests.RequestException as e:
                last_exception = e
                logger.warning(f"Attempt {attempt + 1} failed: {e}")
                if attempt < CONFIG['MAX_RETRIES'] - 1:
                    continue
        
        return False, f"Tất cả attempts thất bại: {last_exception}"
        
    except Exception as e:
        logger.error(f"Lỗi khi gọi FPT API: {e}")
        return False, f"Lỗi không mong muốn: {e}"

def save_pdf_file(pdf_data, filename_prefix="hdn"):
    """
    Lưu dữ liệu PDF vào file với tên theo format
    """
    try:
        # Tạo tên file với timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{filename_prefix}_{timestamp}.pdf"
        filepath = os.path.join(CONFIG['INVOICE_DIR'], filename)
        
        # Kiểm tra xem dữ liệu có phải base64 không
        if isinstance(pdf_data, str):
            # Có thể là base64 string
            try:
                # Loại bỏ prefix data URL nếu có
                if pdf_data.startswith('data:application/pdf;base64,'):
                    pdf_data = pdf_data[len('data:application/pdf;base64,'):]
                
                # Decode base64
                pdf_bytes = base64.b64decode(pdf_data)
                logger.info(f"Decoded base64 PDF (size: {len(pdf_bytes)} bytes)")
                
            except Exception as e:
                logger.error(f"Không thể decode base64: {e}")
                return None, f"Lỗi decode base64: {e}"
        else:
            # Dữ liệu binary
            pdf_bytes = pdf_data
            logger.info(f"Binary PDF data (size: {len(pdf_bytes)} bytes)")
        
        # Lưu file
        with open(filepath, 'wb') as f:
            f.write(pdf_bytes)
        
        logger.info(f"Đã lưu PDF: {filepath}")
        return filename, None
        
    except Exception as e:
        logger.error(f"Lỗi khi lưu PDF: {e}")
        return None, f"Lỗi lưu file: {e}"

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.now().isoformat(),
        'version': '1.0.0'
    })

@app.route('/create-sample-invoice', methods=['POST'])
def create_sample_invoice():
    """
    API endpoint tạo hóa đơn mẫu
    """
    try:
        logger.info("=== BẮT ĐẦU TẠO HÓA ĐƠN ===")
        
        # 1. Validate request
        if not request.is_json:
            return jsonify({
                'success': False,
                'error': 'Content-Type phải là application/json'
            }), 400
        
        invoice_data = request.get_json()
        if not invoice_data:
            return jsonify({
                'success': False,
                'error': 'Request body không được rỗng'
            }), 400
        
        logger.info(f"Nhận được dữ liệu hóa đơn: {len(str(invoice_data))} characters")
        
        # 2. Validate invoice data
        is_valid, validation_msg = validate_invoice_data(invoice_data)
        if not is_valid:
            logger.error(f"Validation failed: {validation_msg}")
            return jsonify({
                'success': False,
                'error': f'Dữ liệu không hợp lệ: {validation_msg}'
            }), 400
        
        logger.info("✅ Validation thành công")
        
        # 3. Load token
        token = load_token()
        if not token:
            return jsonify({
                'success': False,
                'error': 'Không thể load token. Vui lòng đăng nhập lại.'
            }), 401
        
        logger.info("✅ Token loaded thành công")
        
        # 4. Gọi FPT API
        success, result = call_fpt_api(invoice_data, token)
        if not success:
            logger.error(f"FPT API failed: {result}")
            return jsonify({
                'success': False,
                'error': f'Lỗi FPT API: {result}'
            }), 500
        
        logger.info("✅ FPT API thành công")
        
        # 5. Xử lý response và lưu PDF
        response = result
        
        # Thử các cách khác nhau để lấy PDF data
        pdf_data = None
        
        # Cách 1: Response content trực tiếp (binary)
        if response.headers.get('content-type', '').startswith('application/pdf'):
            pdf_data = response.content
            logger.info("PDF data từ response content (binary)")
        
        # Cách 2: Response JSON chứa base64
        else:
            try:
                response_json = response.json()
                
                # Tìm PDF data trong các trường có thể
                possible_fields = ['pdf', 'data', 'content', 'file', 'base64']
                for field in possible_fields:
                    if field in response_json and response_json[field]:
                        pdf_data = response_json[field]
                        logger.info(f"PDF data từ response.{field}")
                        break
                
                if not pdf_data:
                    logger.error(f"Không tìm thấy PDF data trong response JSON: {list(response_json.keys())}")
                    return jsonify({
                        'success': False,
                        'error': 'Không tìm thấy dữ liệu PDF trong response'
                    }), 500
                    
            except Exception as e:
                logger.error(f"Không thể parse response JSON: {e}")
                # Fallback: sử dụng response.text
                pdf_data = response.text
        
        # 6. Lưu PDF file
        filename, save_error = save_pdf_file(pdf_data)
        if save_error:
            return jsonify({
                'success': False,
                'error': save_error
            }), 500
        
        logger.info(f"✅ Đã lưu PDF: {filename}")
        
        # 7. Trả về kết quả
        result = {
            'success': True,
            'pdf_url': f'/view-invoice/{filename}',
            'filename': filename,
            'created_at': datetime.now().isoformat()
        }
        
        logger.info("=== HOÀN THÀNH TẠO HÓA ĐƠN ===")
        return jsonify(result)
        
    except BadRequest as e:
        logger.error(f"Bad request: {e}")
        return jsonify({
            'success': False,
            'error': 'Request không hợp lệ'
        }), 400
        
    except Exception as e:
        logger.error(f"Lỗi không mong muốn: {e}")
        logger.error(f"Traceback: {traceback.format_exc()}")
        return jsonify({
            'success': False,
            'error': f'Lỗi server: {str(e)}'
        }), 500

@app.route('/view-invoice/<filename>', methods=['GET'])
def view_invoice(filename):
    """
    API endpoint xem hóa đơn PDF
    """
    try:
        logger.info(f"Request xem PDF: {filename}")

        # Validate filename
        if not filename.endswith('.pdf'):
            abort(400, description="File phải có extension .pdf")

        # Security: chỉ cho phép filename an toàn
        if '..' in filename or '/' in filename or '\\' in filename:
            abort(400, description="Filename không hợp lệ")

        filepath = os.path.join(CONFIG['INVOICE_DIR'], filename)

        # Kiểm tra file tồn tại
        if not os.path.exists(filepath):
            logger.warning(f"File không tồn tại: {filepath}")
            abort(404, description="File không tồn tại")

        # Kiểm tra file có phải PDF không
        try:
            with open(filepath, 'rb') as f:
                header = f.read(4)
                if header != b'%PDF':
                    abort(400, description="File không phải PDF hợp lệ")
        except Exception as e:
            logger.error(f"Lỗi kiểm tra file: {e}")
            abort(500, description="Lỗi đọc file")

        logger.info(f"✅ Trả về PDF: {filepath}")

        # Trả về file PDF với headers phù hợp
        return send_file(
            filepath,
            mimetype='application/pdf',
            as_attachment=False,  # Hiển thị inline trong browser
            download_name=filename
        )

    except Exception as e:
        logger.error(f"Lỗi khi xem PDF: {e}")
        abort(500, description=f"Lỗi server: {str(e)}")

@app.route('/list-invoices', methods=['GET'])
def list_invoices():
    """
    API endpoint liệt kê các hóa đơn đã tạo
    """
    try:
        logger.info("Request danh sách hóa đơn")

        # Lấy danh sách file PDF
        invoice_files = []

        if os.path.exists(CONFIG['INVOICE_DIR']):
            for filename in os.listdir(CONFIG['INVOICE_DIR']):
                if filename.endswith('.pdf'):
                    filepath = os.path.join(CONFIG['INVOICE_DIR'], filename)

                    # Lấy thông tin file
                    stat = os.stat(filepath)
                    file_info = {
                        'filename': filename,
                        'size': stat.st_size,
                        'created_at': datetime.fromtimestamp(stat.st_ctime).isoformat(),
                        'modified_at': datetime.fromtimestamp(stat.st_mtime).isoformat(),
                        'view_url': f'/view-invoice/{filename}'
                    }
                    invoice_files.append(file_info)

        # Sắp xếp theo thời gian tạo (mới nhất trước)
        invoice_files.sort(key=lambda x: x['created_at'], reverse=True)

        logger.info(f"Tìm thấy {len(invoice_files)} hóa đơn")

        return jsonify({
            'success': True,
            'count': len(invoice_files),
            'invoices': invoice_files
        })

    except Exception as e:
        logger.error(f"Lỗi khi liệt kê hóa đơn: {e}")
        return jsonify({
            'success': False,
            'error': f'Lỗi server: {str(e)}'
        }), 500

@app.route('/token-info', methods=['GET'])
def token_info():
    """
    API endpoint kiểm tra thông tin token
    """
    try:
        logger.info("Request thông tin token")

        if not os.path.exists(CONFIG['TOKEN_FILE']):
            return jsonify({
                'success': False,
                'error': 'Token file không tồn tại'
            }), 404

        with open(CONFIG['TOKEN_FILE'], 'r', encoding='utf-8') as f:
            token_data = json.load(f)

        # Ẩn token thực tế vì lý do bảo mật
        safe_token_data = {
            'token_type': token_data.get('token_type'),
            'token_source': token_data.get('token_source'),
            'extracted_at': token_data.get('extracted_at'),
            'origin': token_data.get('origin'),
            'token_length': token_data.get('token_length'),
            'token_preview': token_data.get('token_preview'),
            'has_token': bool(token_data.get('token'))
        }

        return jsonify({
            'success': True,
            'token_info': safe_token_data
        })

    except Exception as e:
        logger.error(f"Lỗi khi lấy thông tin token: {e}")
        return jsonify({
            'success': False,
            'error': f'Lỗi server: {str(e)}'
        }), 500

@app.errorhandler(404)
def not_found(error):
    return jsonify({
        'success': False,
        'error': 'Endpoint không tồn tại'
    }), 404

@app.errorhandler(405)
def method_not_allowed(error):
    return jsonify({
        'success': False,
        'error': 'Method không được hỗ trợ'
    }), 405

@app.errorhandler(413)
def request_entity_too_large(error):
    return jsonify({
        'success': False,
        'error': 'Request quá lớn (max 16MB)'
    }), 413

@app.errorhandler(500)
def internal_server_error(error):
    logger.error(f"Internal server error: {error}")
    return jsonify({
        'success': False,
        'error': 'Lỗi server nội bộ'
    }), 500

if __name__ == '__main__':
    print("🚀 Khởi động FPT eInvoice API Server")
    print(f"📁 Invoice directory: {CONFIG['INVOICE_DIR']}")
    print(f"🔑 Token file: {CONFIG['TOKEN_FILE']}")
    print("="*60)

    # Kiểm tra token khi khởi động
    token = load_token()
    if token:
        print("✅ Token sẵn sàng")
    else:
        print("⚠️ Không có token - cần đăng nhập trước")

    print("🌐 Server sẽ chạy tại: http://localhost:5000")
    print("📋 Endpoints:")
    print("   - GET  /health")
    print("   - GET  /token-info")
    print("   - GET  /list-invoices")
    print("   - POST /create-sample-invoice")
    print("   - GET  /view-invoice/<filename>")
    print("="*60)

    # Chạy server
    app.run(
        host='0.0.0.0',
        port=5000,
        debug=False,  # Tắt debug để giảm output
        threaded=True
    )
