# FPT eInvoice Integration Project

Hệ thống tích hợp hoàn chỉnh với FPT eInvoice để tự động đăng nhập, trích xuất token và tạo hóa đơn PDF với tính năng bảo mật và xử lý concurrent requests.

## 🚀 Tính năng chính

### 🤖 Automation & Authentication
- ✅ **Đăng nhập tự động** vào FPT eInvoice portal bằng Selenium
- ✅ **Trích xuất JWT token** từ browser session storage
- ✅ **Auto Re-Authentication** khi token hết hạn
- ✅ **Thread-safe concurrent handling** cho multiple requests

### 🌐 API Server
- ✅ **RESTful API** để tạo hóa đơn PDF từ JSON data
- ✅ **Dual PDF modes** - lưu file hoặc stream trực tiếp
- ✅ **API Key Authentication** bảo vệ endpoints
- ✅ **Smart retry logic** với error handling

### 🔒 Security & Reliability
- ✅ **Environment-based configuration** (.env files)
- ✅ **Concurrent request management** (singleton pattern)
- ✅ **Comprehensive logging** và monitoring
- ✅ **Production-ready** với robust error handling

## 📁 Cấu trúc project

```
├── selenium_automation.py           # 🤖 Đăng nhập tự động và trích xuất token
├── api_server.py                   # 🌐 API server với authentication và retry logic
├── pdf_viewer.py                   # 📄 Hiển thị PDF từ response.json
├── .env                           # 🔒 Environment variables (KHÔNG commit)
├── .env.example                   # 📋 Template cấu hình
├── .gitignore                     # 🚫 Ignore sensitive files
├── session_token.json             # 🔑 JWT token (tự động tạo)
├── sample_invoice.json            # 📋 Dữ liệu hóa đơn mẫu
├── requirements.txt               # 📦 Python dependencies
├── invoices/                      # 📁 Thư mục lưu PDF (KHÔNG commit)
├── sample/                        # 📁 Dữ liệu mẫu từ FPT
├── README.md                      # 📖 Tài liệu hoàn chỉnh (file này)
├── API_AUTHENTICATION_GUIDE.md    # 🔐 Hướng dẫn API authentication
└── AUTO_REAUTH_GUIDE.md           # 🔄 Hướng dẫn auto re-authentication
```

## 🔧 Cài đặt

### 1. Cài đặt dependencies

```bash
pip install -r requirements.txt
```

### 2. Cấu hình thông tin đăng nhập

Tạo file `.env` từ template:

```bash
cp .env.example .env
```

Chỉnh sửa file `.env` với thông tin thực tế:

```env
# Authentication Credentials
FPT_MASOTHUE=your_tax_code_here      # Mã số thuế
FPT_USERNAME=your_username_here      # Tên đăng nhập
FPT_PASSWORD=your_password_here      # Mật khẩu
```

**⚠️ Lưu ý:** File `.env` chứa thông tin nhạy cảm và đã được thêm vào `.gitignore`

### 3. Cấu hình API Authentication

Thêm API key vào file `.env`:

```env
# API Authentication
API_AUTH_KEY=your_secret_api_key_here
```

**🔒 Bảo mật:** Thay đổi API key này trong production và không chia sẻ với người khác.

## 📖 Cách sử dụng

### Bước 1: Đăng nhập và lấy token

```bash
python selenium_automation.py
```

**Kết quả:**
- Tự động đăng nhập vào FPT eInvoice portal
- Trích xuất JWT token từ session storage
- Lưu token vào file `session_token.json`
- Token sẽ được sử dụng cho API calls

### Bước 2: Khởi động API server

```bash
python api_server.py
```

**Server chạy tại:** `http://localhost:5959`

**Server features:**
- 🔒 API Key authentication enabled
- 🔄 Auto re-authentication khi token hết hạn
- 🚀 Concurrent request handling
- 📊 Comprehensive logging

### Bước 3: Tạo hóa đơn PDF

#### Cách 1: Lưu file và xem sau
```bash
curl -X POST http://localhost:5959/create-sample-invoice \
  -H "Authorization: Bearer <API_KEY>" \
  -H "Content-Type: application/json" \
  -d @sample_invoice.json
```

#### Cách 2: Xem trực tiếp (Recommended)
```bash
curl -X POST http://localhost:5959/create-view-sample-invoice \
  -H "Authorization: Bearer <API_KEY>" \
  -H "Content-Type: application/json" \
  -d @sample_invoice.json \
  --output invoice.pdf
```

#### Sử dụng Python:
```python
import requests
import json
import os

# Lấy API key từ environment
api_key = os.getenv('API_AUTH_KEY')
headers = {'Authorization': f'Bearer {api_key}'}

with open('sample_invoice.json', 'r') as f:
    data = json.load(f)

# Cách 1: Lưu file
response = requests.post(
    'http://localhost:5959/create-sample-invoice',
    headers=headers,
    json=data
)
result = response.json()
print(f"PDF URL: {result['pdf_url']}")

# Cách 2: Nhận PDF trực tiếp
response = requests.post(
    'http://localhost:5959/create-view-sample-invoice',
    headers=headers,
    json=data
)
with open('invoice.pdf', 'wb') as f:
    f.write(response.content)
```

### Bước 4: Xem PDF

- **Trực tiếp:** Gọi `/create-view-sample-invoice` và mở trong browser
- **Từ file:** Truy cập `http://localhost:5959/view-invoice/{filename}`

## 🌐 API Endpoints

### 🔒 Authentication Required

Tất cả API endpoints (trừ `/` và `/health`) yêu cầu API key authentication:

#### Authentication Methods:

**1. Authorization Bearer Header:**
```bash
curl -H "Authorization: Bearer <API_KEY>" http://localhost:5959/token-info
```

**2. X-API-Key Header:**
```bash
curl -H "X-API-Key: <API_KEY>" http://localhost:5959/token-info
```

#### Endpoint Protection:

**Public Endpoints (No Auth Required):**
- ✅ `GET /` - Home page
- ✅ `GET /health` - Health check

**Protected Endpoints (Auth Required):**
- 🔒 `GET /token-info` - Token information
- 🔒 `POST /create-sample-invoice` - Tạo hóa đơn và lưu file
- 🔒 `POST /create-view-sample-invoice` - Tạo và xem hóa đơn trực tiếp
- 🔒 `GET /view-invoice/<filename>` - Xem PDF đã lưu

#### Error Responses:

**Missing Authentication:**
```json
{
  "success": false,
  "error": "Authentication required"
}
```

**Invalid API Key:**
```json
{
  "success": false,
  "error": "Invalid API key"
}
```

### 1. Health Check (Public)
```http
GET /health
```

### 2. Token Info (🔒 Auth Required)
```http
GET /token-info
Authorization: Bearer <API_KEY>
```

### 3. Tạo hóa đơn (lưu file) (🔒 Auth Required)
```http
POST /create-sample-invoice
Authorization: Bearer <API_KEY>
Content-Type: application/json

{
  "buyer": {...},
  "seller": {...},
  "items": [...],
  "amounts": {...}
}
```

### 4. Tạo và xem hóa đơn trực tiếp (🔒 Auth Required)
```http
POST /create-view-sample-invoice
Authorization: Bearer <API_KEY>
Content-Type: application/json

{
  "buyer": {...},
  "seller": {...},
  "items": [...],
  "amounts": {...}
}
```
**Response:** PDF file stream (hiển thị trực tiếp trong browser)

### 5. Xem PDF đã lưu (🔒 Auth Required)
```http
GET /view-invoice/{filename}
Authorization: Bearer <API_KEY>
```

## 📋 Dữ liệu hóa đơn mẫu

File `sample_invoice.json` chứa cấu trúc dữ liệu hoàn chỉnh:

```json
{
  "buyer": {
    "name": "CÔNG TY TNHH ABC",
    "address": "123 Đường ABC, TP.HCM",
    "tax_code": "0123456789"
  },
  "seller": {
    "name": "CÔNG TY TNHH XYZ", 
    "address": "456 Đường XYZ, Hà Nội",
    "tax_code": "0987654321"
  },
  "items": [
    {
      "name": "Dịch vụ kho bãi",
      "quantity": 1,
      "unit_price": 5000000,
      "tax_rate": 10
    }
  ],
  "amounts": {
    "subtotal": 7500000,
    "tax_amount": 750000,
    "total": 8250000
  }
}
```

## 🔒 Bảo mật & Auto Re-Authentication

- ✅ **API Key Authentication** - Bảo vệ tất cả endpoints quan trọng
- ✅ **JWT Token** được lưu trữ cục bộ
- ✅ **Thông tin đăng nhập** không được commit vào git
- ✅ **File validation** để tránh path traversal
- ✅ **Auto Re-Authentication** khi token hết hạn
- ✅ **Retry Logic** tự động với error handling thông minh
- ✅ **Token Expiry Detection** - tự động phát hiện lỗi 401/500
- ✅ **Selenium Auto-Run** - tự động chạy script đăng nhập khi cần
- ✅ **Authorization Headers** - Hỗ trợ Bearer token và X-API-Key

## 🤖 Auto Re-Authentication

### Tính năng tự động
API server có khả năng **tự động đăng nhập lại** khi token hết hạn:

1. **Phát hiện token hết hạn** - Khi nhận HTTP 401/500 từ FPT API
2. **Tự động chạy Selenium** - Gọi `python selenium_automation.py`
3. **Lấy token mới** - Cập nhật file `session_token.json`
4. **Retry API call** - Thử lại với token mới
5. **Trả về kết quả** - Hoạt động như bình thường

### Workflow tự động:
```
API Call → Token Expired → Auto Login → New Token → Retry → Success
```

### 🔄 Concurrent Request Handling

**Problem Solved:**
- ❌ Multiple selenium processes chạy cùng lúc
- ❌ Race condition khi ghi file `session_token.json`
- ❌ Lãng phí tài nguyên

**Solution Implemented:**
```python
# Singleton pattern cho re-authentication
with _auth_lock:
    if _auth_in_progress:
        # Waiting threads
        _waiting_count += 1
        current_event.wait(timeout=150)
    else:
        # First thread runs selenium
        _auth_in_progress = True
        # ... run selenium ...
        current_event.set()  # Notify all waiting threads
```

**Concurrent Workflow:**
```
Request 1 (Token Expired) → Runs Selenium → Gets New Token
Request 2 (Token Expired) → Waits for Request 1 → Uses New Token
Request 3 (Token Expired) → Waits for Request 1 → Uses New Token
Request 4 (Token Expired) → Waits for Request 1 → Uses New Token
Request 5 (Token Expired) → Waits for Request 1 → Uses New Token
```

**⚡ Lợi ích:**
- Không cần manual refresh token
- API hoạt động liên tục 24/7
- Xử lý concurrent requests hiệu quả
- Thread-safe authentication
- Không có race conditions
- Resource efficient (chỉ 1 selenium process)
- Fair queuing cho tất cả requests
- Logging chi tiết

### Performance Impact:
- **Normal Operation**: ~2-5 seconds
- **First Request (Token Expired)**: ~60-120 seconds (triggers selenium)
- **Concurrent Requests**: ~60-150 seconds (wait for first request)
- **Subsequent Calls**: ~2-5 seconds (with new token)

## 🐛 Troubleshooting

### 🔑 Authentication Issues

#### "Authentication required" error:
```bash
# Kiểm tra header có được include không
curl -v http://localhost:5959/token-info
# Should include: Authorization: Bearer <key> or X-API-Key: <key>
```

#### "Invalid API key" error:
```bash
# Kiểm tra .env file
cat .env | grep API_AUTH_KEY

# Kiểm tra environment loading
python -c "import os; from dotenv import load_dotenv; load_dotenv(); print(os.getenv('API_AUTH_KEY'))"
```

### 🔄 Auto Re-Authentication Issues

#### "Re-authentication failed":
```bash
# Kiểm tra thông tin đăng nhập trong .env
cat .env

# Test selenium manual
python selenium_automation.py

# Cập nhật Chrome driver
pip install --upgrade webdriver-manager
```

#### Selenium timeout:
- Tăng timeout trong `api_server.py` (hiện tại: 120s)
- Kiểm tra Chrome browser có cài đặt không
- Kiểm tra network connection
- Kiểm tra FPT eInvoice portal có accessible không

### 🌐 API Server Issues

#### "FPT API failed":
- ✅ **Auto retry** - API tự động thử lại với token mới
- ✅ **Smart detection** - Phát hiện lỗi token vs lỗi khác
- ⚠️ Kiểm tra kết nối internet nếu vẫn lỗi
- ⚠️ Kiểm tra format dữ liệu JSON

#### Server startup error:
```bash
# Kiểm tra API_AUTH_KEY có được set không
python -c "import os; print('API_AUTH_KEY' in os.environ)"

# Kiểm tra import
python -c "import api_server; print('Import OK')"
```

#### Concurrent request issues:
- Kiểm tra logs để xem queue status
- Verify chỉ 1 selenium process chạy
- Check waiting thread timeouts (150s)

### 📊 Monitoring & Debugging

#### Check server logs:
```bash
# Theo dõi logs real-time
python api_server.py

# Key log messages to look for:
# ✅ Authentication successful for endpoint: token_info
# 🔄 FPT API attempt 1/2
# 🚀 Bắt đầu authentication process (first thread)
# ⏳ Authentication đang chạy, waiting... (queue: 3)
# 📢 Notified 3 waiting threads
```

#### Test endpoints manually:
```bash
# Test public endpoints
curl http://localhost:5959/health

# Test protected endpoints
curl -H "Authorization: Bearer <API_KEY>" http://localhost:5959/token-info

# Test create invoice
curl -X POST -H "Authorization: Bearer <API_KEY>" \
     -H "Content-Type: application/json" \
     -d @sample_invoice.json \
     http://localhost:5959/create-sample-invoice
```

## 📊 Logging & Monitoring

### Log Levels:
- `INFO`: Thông tin chung, authentication success, API calls
- `WARNING`: Cảnh báo, token expiry detection
- `ERROR`: Lỗi, authentication failures, API errors

### Key Log Messages:
```
# Authentication
✅ API authentication enabled (key length: 32)
✅ Authentication successful for endpoint: token_info
🔒 Authentication failed: Missing API key
🔒 Authentication failed: Invalid API key (length: 10)

# Auto Re-Authentication
🔄 FPT API attempt 1/2
⚠️ Token expired error detected: 500
🚀 Bắt đầu authentication process (first thread)
⏳ Authentication đang chạy, waiting... (queue: 3)
🏁 Authentication hoàn thành: True - Token refreshed
📢 Notified 3 waiting threads
✅ Received authentication result: True - Token refreshed

# API Operations
✅ FPT API thành công
📄 Returning PDF directly: hdn_20250622_143052.pdf (93047 bytes)
💾 Saved PDF: ./invoices/hdn_20250622_143052.pdf
```

### Security Logging:
- ✅ **No key logging** - Actual key values không được log
- ✅ **Length logging** - Chỉ log độ dài key để debug
- ✅ **Endpoint tracking** - Log endpoint nào được access
- ✅ **Failure tracking** - Log authentication failures

## 🔐 Security Best Practices

### 1. API Key Management:
- 🔐 **Strong keys** - Sử dụng key dài và phức tạp (>20 characters)
- 🔄 **Regular rotation** - Thay đổi key định kỳ
- 🚫 **No hardcoding** - Không hardcode key trong source code
- 📝 **Environment only** - Chỉ lưu trong .env file
- 🗂️ **Different keys** - Sử dụng key khác nhau cho dev/staging/prod

### 2. Production Deployment:
- 🌐 **HTTPS only** - Luôn sử dụng HTTPS trong production
- 🔒 **Secure storage** - Lưu key trong secure environment variables
- 📊 **Monitor access** - Track API usage và authentication failures
- 🚨 **Alert system** - Cảnh báo khi có unauthorized attempts
- 🛡️ **Rate limiting** - Implement rate limiting cho API endpoints
- 🔍 **Audit logging** - Log tất cả API access attempts

### 3. Development Security:
- 🧪 **Test keys** - Sử dụng key khác nhau cho development
- 📋 **Documentation** - Document API key requirements clearly
- 🔍 **Validation** - Test authentication thoroughly
- 🚫 **Git ignore** - Đảm bảo .env không được commit
- 🔐 **Local encryption** - Encrypt sensitive local files

### 4. Network Security:
- 🌐 **HTTPS/TLS** - Always use encrypted connections
- 🔒 **VPN access** - Restrict API access via VPN if possible
- 🚫 **IP whitelisting** - Limit access to known IP ranges
- 📊 **Traffic monitoring** - Monitor for suspicious patterns

## 🔄 Workflow hoàn chỉnh

### Basic Workflow:
1. **Đăng nhập** → `selenium_automation.py`
2. **Lấy token** → `session_token.json`
3. **Khởi động API** → `api_server.py`
4. **Tạo hóa đơn** → POST `/create-sample-invoice`
5. **Lưu PDF** → `./invoices/hdn_YYYYMMDD_HHMMSS.pdf`
6. **Xem PDF** → GET `/view-invoice/{filename}`

### Advanced Workflow với Auto Re-Auth:
1. **Initial Setup** → Configure .env, start server
2. **API Call** → Client gọi API với authentication
3. **Token Check** → Server validate JWT token
4. **Auto Re-Auth** → Nếu token expired, tự động refresh
5. **Concurrent Handling** → Multiple requests được queue properly
6. **Response** → Return PDF hoặc error với proper status codes

### Production Workflow:
1. **Environment Setup** → Secure .env configuration
2. **Server Deployment** → HTTPS, monitoring, logging
3. **API Integration** → Client applications integrate với proper auth
4. **Monitoring** → Track usage, errors, performance
5. **Maintenance** → Regular key rotation, updates

## 🚀 Advanced Usage

### 1. Integration Examples:

#### Web Application Integration:
```javascript
// Frontend JavaScript
const apiKey = 'your_api_key_here';

async function createInvoice(invoiceData) {
    try {
        const response = await fetch('https://your-domain.com/create-view-sample-invoice', {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${apiKey}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(invoiceData)
        });

        if (response.ok) {
            const blob = await response.blob();
            const url = URL.createObjectURL(blob);
            window.open(url); // Open PDF in new tab
        } else {
            const error = await response.json();
            console.error('API Error:', error.error);
        }
    } catch (error) {
        console.error('Network Error:', error);
    }
}
```

#### Python Application Integration:
```python
import requests
import os
from typing import Dict, Any, Optional

class FPTInvoiceClient:
    def __init__(self, base_url: str = "http://localhost:5959"):
        self.base_url = base_url
        self.api_key = os.getenv('API_AUTH_KEY')
        self.headers = {'Authorization': f'Bearer {self.api_key}'}

    def create_invoice(self, invoice_data: Dict[str, Any]) -> Optional[bytes]:
        """Create invoice and return PDF bytes"""
        try:
            response = requests.post(
                f"{self.base_url}/create-view-sample-invoice",
                headers=self.headers,
                json=invoice_data,
                timeout=180
            )

            if response.status_code == 200:
                return response.content
            else:
                error = response.json()
                raise Exception(f"API Error: {error.get('error')}")

        except Exception as e:
            print(f"Error creating invoice: {e}")
            return None

    def save_invoice(self, invoice_data: Dict[str, Any], filename: str) -> bool:
        """Create invoice and save to file"""
        pdf_bytes = self.create_invoice(invoice_data)
        if pdf_bytes:
            with open(filename, 'wb') as f:
                f.write(pdf_bytes)
            return True
        return False

# Usage
client = FPTInvoiceClient()
invoice_data = {...}  # Your invoice data
success = client.save_invoice(invoice_data, "invoice.pdf")
```

### 2. Batch Processing:
```python
import asyncio
import aiohttp
from concurrent.futures import ThreadPoolExecutor

async def process_invoices_batch(invoice_list):
    """Process multiple invoices concurrently"""
    api_key = os.getenv('API_AUTH_KEY')
    headers = {'Authorization': f'Bearer {api_key}'}

    async with aiohttp.ClientSession() as session:
        tasks = []
        for invoice_data in invoice_list:
            task = create_invoice_async(session, headers, invoice_data)
            tasks.append(task)

        results = await asyncio.gather(*tasks, return_exceptions=True)
        return results

async def create_invoice_async(session, headers, invoice_data):
    """Async invoice creation"""
    try:
        async with session.post(
            'http://localhost:5959/create-view-sample-invoice',
            headers=headers,
            json=invoice_data,
            timeout=180
        ) as response:
            if response.status == 200:
                return await response.read()
            else:
                error = await response.json()
                raise Exception(f"API Error: {error.get('error')}")
    except Exception as e:
        return e
```

## 🔮 Future Enhancements

### Planned Features:
- 🔄 **Token Refresh Schedule** - Refresh token trước khi hết hạn
- 📊 **Metrics Dashboard** - Monitor success rate, response time
- 🔔 **Alerting System** - Notify khi re-auth fails
- 🏃 **Headless Mode** - Selenium chạy background
- 💾 **Token Caching** - Cache multiple tokens
- 🔐 **Token Encryption** - Encrypt token storage
- 🌐 **Multi-tenant Support** - Support multiple FPT accounts
- 📱 **Mobile API** - Optimized endpoints cho mobile apps

### Performance Optimizations:
- ⚡ **Async Processing** - Non-blocking re-authentication
- 🚀 **Connection Pooling** - Reuse HTTP connections
- 📦 **Response Caching** - Cache PDF responses
- 🎯 **Smart Retry** - Exponential backoff
- 🔄 **Load Balancing** - Multiple server instances
- 📊 **Database Integration** - Store invoices in database

### Security Enhancements:
- 🔐 **OAuth2 Integration** - Modern authentication
- 🛡️ **Rate Limiting** - Prevent abuse
- 🔍 **Audit Logging** - Comprehensive audit trails
- 🚨 **Intrusion Detection** - Monitor suspicious activity
- 🔒 **End-to-End Encryption** - Encrypt all communications

## 📞 Support & Documentation

### 📖 Additional Documentation:
- **API_AUTHENTICATION_GUIDE.md** - Chi tiết về API authentication
- **AUTO_REAUTH_GUIDE.md** - Hướng dẫn auto re-authentication
- **sample_invoice.json** - Dữ liệu hóa đơn mẫu
- **.env.example** - Template cấu hình environment

### 🆘 Getting Help:

#### Common Issues:
1. **Authentication Problems** → Check API key in .env
2. **Token Expiry** → API tự động handle, check logs
3. **Selenium Issues** → Update Chrome driver, check network
4. **PDF Generation Fails** → Verify invoice data format
5. **Server Won't Start** → Check environment variables

#### Debug Steps:
```bash
# 1. Check environment
python -c "import os; from dotenv import load_dotenv; load_dotenv(); print('API_KEY:', bool(os.getenv('API_AUTH_KEY')))"

# 2. Test selenium
python selenium_automation.py

# 3. Test server
python api_server.py

# 4. Test API
curl -H "Authorization: Bearer $(grep API_AUTH_KEY .env | cut -d= -f2)" http://localhost:5959/health
```

#### Contact Information:
- 📧 **FPT eInvoice Support**: <EMAIL>
- 🌐 **FPT eInvoice Portal**: https://einvoice.fpt.com.vn
- 📚 **API Documentation**: https://portal.einvoice.fpt.com.vn/api/docs

---

## ⚠️ Important Notes

### Environment:
- **UAT Environment** - Đây là môi trường User Acceptance Testing
- **Production Ready** - Code đã sẵn sàng cho production với proper configuration
- **Security First** - Luôn sử dụng HTTPS và secure key management trong production

### Data Handling:
- **Test Data Only** - Không sử dụng dữ liệu thật trong môi trường test
- **Token Expiry** - JWT tokens có thời hạn, được auto-refresh
- **File Management** - PDF files được lưu local, cân nhắc cloud storage cho production

### Performance:
- **Concurrent Safe** - Hỗ trợ multiple concurrent requests
- **Auto Recovery** - Tự động xử lý token expiry và network issues
- **Scalable Design** - Architecture cho phép scale horizontal

---

## 🏆 Project Status

### ✅ Completed Features:
- [x] Selenium automation cho FPT eInvoice login
- [x] JWT token extraction và management
- [x] RESTful API server với Flask
- [x] Dual PDF modes (save file / stream direct)
- [x] API Key authentication
- [x] Auto re-authentication với retry logic
- [x] Thread-safe concurrent request handling
- [x] Comprehensive error handling
- [x] Environment-based configuration
- [x] Production-ready logging
- [x] Security best practices implementation

### 🔄 In Progress:
- [ ] Performance monitoring dashboard
- [ ] Advanced caching mechanisms
- [ ] Multi-tenant support

### 🎯 Future Roadmap:
- [ ] OAuth2 integration
- [ ] Database integration
- [ ] Mobile API optimization
- [ ] Advanced analytics

---

**🚀 FPT eInvoice Integration Project - Production Ready với Auto Re-Authentication, API Security, và Concurrent Request Handling!**

*Developed with ❤️ for seamless FPT eInvoice integration*
