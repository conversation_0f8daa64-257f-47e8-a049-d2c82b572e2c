#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Chương trình hiển thị file PDF từ response.json
Phương án 1: Giải mã và lưu file PDF, sau đó mở bằng trình xem PDF mặc định
"""

import json
import base64
import os
import tempfile
import platform
import subprocess
import sys
from pathlib import Path

def load_response_json(file_path="response.json"):
    """
    Đọc file response.json và trả về dữ liệu JSON
    
    Args:
        file_path (str): Đường dẫn đến file response.json
        
    Returns:
        dict: Dữ liệu JSON đã được parse
        
    Raises:
        FileNotFoundError: Nếu file không tồn tại
        json.JSONDecodeError: Nếu file JSON không hợp lệ
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        return data
    except FileNotFoundError:
        print(f"Lỗi: Không tìm thấy file {file_path}")
        raise
    except json.JSONDecodeError as e:
        print(f"Lỗi: File JSON không hợp lệ - {e}")
        raise

def extract_pdf_data(json_data):
    """
    Trích xuất dữ liệu PDF từ JSON
    
    Args:
        json_data (dict): Dữ liệu JSON
        
    Returns:
        str: Chuỗi base64 của PDF (đã loại bỏ prefix data:application/pdf;base64,)
        
    Raises:
        KeyError: Nếu không tìm thấy key 'pdf'
        ValueError: Nếu dữ liệu PDF không hợp lệ
    """
    if 'pdf' not in json_data:
        raise KeyError("Không tìm thấy key 'pdf' trong dữ liệu JSON")
    
    pdf_data = json_data['pdf']
    
    # Kiểm tra và loại bỏ prefix data URL nếu có
    if pdf_data.startswith('data:application/pdf;base64,'):
        pdf_data = pdf_data[len('data:application/pdf;base64,'):]
    elif pdf_data.startswith('data:application/pdf;base64, '):
        pdf_data = pdf_data[len('data:application/pdf;base64, '):]
    
    # Loại bỏ khoảng trắng
    pdf_data = pdf_data.strip()
    
    if not pdf_data:
        raise ValueError("Dữ liệu PDF trống")
    
    return pdf_data

def decode_pdf_base64(base64_data):
    """
    Giải mã dữ liệu base64 thành bytes PDF
    
    Args:
        base64_data (str): Chuỗi base64
        
    Returns:
        bytes: Dữ liệu PDF đã giải mã
        
    Raises:
        ValueError: Nếu không thể giải mã base64
    """
    try:
        pdf_bytes = base64.b64decode(base64_data)
        return pdf_bytes
    except Exception as e:
        raise ValueError(f"Không thể giải mã base64: {e}")

def save_pdf_to_temp(pdf_bytes, filename="invoice_preview.pdf"):
    """
    Lưu dữ liệu PDF vào file tạm thời
    
    Args:
        pdf_bytes (bytes): Dữ liệu PDF
        filename (str): Tên file PDF
        
    Returns:
        str: Đường dẫn đến file PDF tạm thời
        
    Raises:
        IOError: Nếu không thể ghi file
    """
    try:
        # Tạo thư mục tạm thời
        temp_dir = tempfile.gettempdir()
        pdf_path = os.path.join(temp_dir, filename)
        
        # Ghi file PDF
        with open(pdf_path, 'wb') as f:
            f.write(pdf_bytes)
        
        print(f"Đã lưu file PDF tạm thời: {pdf_path}")
        return pdf_path
        
    except Exception as e:
        raise IOError(f"Không thể lưu file PDF: {e}")

def open_pdf_with_default_viewer(pdf_path):
    """
    Mở file PDF bằng trình xem mặc định của hệ thống
    
    Args:
        pdf_path (str): Đường dẫn đến file PDF
        
    Returns:
        bool: True nếu mở thành công, False nếu thất bại
    """
    try:
        system = platform.system()
        
        if system == "Windows":
            # Windows: sử dụng start
            os.startfile(pdf_path)
        elif system == "Darwin":
            # macOS: sử dụng open
            subprocess.run(["open", pdf_path], check=True)
        elif system == "Linux":
            # Linux: sử dụng xdg-open
            subprocess.run(["xdg-open", pdf_path], check=True)
        else:
            print(f"Hệ điều hành không được hỗ trợ: {system}")
            return False
        
        print(f"Đã mở file PDF bằng trình xem mặc định")
        return True
        
    except Exception as e:
        print(f"Lỗi khi mở file PDF: {e}")
        return False

def cleanup_temp_file(pdf_path, delay_seconds=30):
    """
    Xóa file tạm thời sau một khoảng thời gian
    
    Args:
        pdf_path (str): Đường dẫn đến file cần xóa
        delay_seconds (int): Thời gian chờ trước khi xóa (giây)
    """
    import time
    import threading
    
    def delayed_cleanup():
        try:
            time.sleep(delay_seconds)
            if os.path.exists(pdf_path):
                os.remove(pdf_path)
                print(f"Đã xóa file tạm thời: {pdf_path}")
        except Exception as e:
            print(f"Không thể xóa file tạm thời: {e}")
    
    # Chạy cleanup trong thread riêng để không block chương trình chính
    cleanup_thread = threading.Thread(target=delayed_cleanup, daemon=True)
    cleanup_thread.start()

def main():
    """
    Hàm chính của chương trình
    """
    try:
        print("=== CHƯƠNG TRÌNH HIỂN THỊ PDF TỪ RESPONSE.JSON ===")
        print("Phương án 1: Giải mã và lưu file PDF, sau đó mở bằng trình xem PDF mặc định")
        print()
        
        # Bước 1: Đọc file response.json
        print("1. Đang đọc file response.json...")
        json_data = load_response_json()
        print("   ✓ Đã đọc thành công file response.json")
        
        # Bước 2: Trích xuất dữ liệu PDF
        print("2. Đang trích xuất dữ liệu PDF...")
        pdf_base64 = extract_pdf_data(json_data)
        print(f"   ✓ Đã trích xuất dữ liệu PDF (kích thước: {len(pdf_base64)} ký tự base64)")
        
        # Bước 3: Giải mã base64
        print("3. Đang giải mã dữ liệu base64...")
        pdf_bytes = decode_pdf_base64(pdf_base64)
        print(f"   ✓ Đã giải mã thành công (kích thước: {len(pdf_bytes)} bytes)")
        
        # Bước 4: Lưu file tạm thời
        print("4. Đang lưu file PDF tạm thời...")
        pdf_path = save_pdf_to_temp(pdf_bytes)
        print(f"   ✓ Đã lưu file tạm thời: {pdf_path}")
        
        # Bước 5: Mở file PDF
        print("5. Đang mở file PDF bằng trình xem mặc định...")
        success = open_pdf_with_default_viewer(pdf_path)
        
        if success:
            print("   ✓ Đã mở file PDF thành công!")
            print()
            print("Lưu ý: File PDF tạm thời sẽ được tự động xóa sau 30 giây.")
            print("Nếu bạn muốn giữ file, hãy sao chép nó sang vị trí khác.")
            
            # Thiết lập cleanup tự động
            cleanup_temp_file(pdf_path, delay_seconds=30)
            
        else:
            print("   ✗ Không thể mở file PDF bằng trình xem mặc định")
            print(f"   Bạn có thể mở thủ công file tại: {pdf_path}")
        
        print()
        print("=== HOÀN THÀNH ===")
        
    except Exception as e:
        print(f"Lỗi: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
