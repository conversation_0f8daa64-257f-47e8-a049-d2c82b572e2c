#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script cho FPT eInvoice API Server
"""

import json
import requests
import time
from datetime import datetime

# Cấu hình
API_BASE_URL = "http://localhost:5000"
SAMPLE_INVOICE_FILE = "sample_invoice.json"

def test_health_check():
    """Test health check endpoint"""
    print("=== TEST HEALTH CHECK ===")
    try:
        response = requests.get(f"{API_BASE_URL}/health")
        print(f"Status: {response.status_code}")
        print(f"Response: {response.json()}")
        return response.status_code == 200
    except Exception as e:
        print(f"Error: {e}")
        return False

def test_token_info():
    """Test token info endpoint"""
    print("\n=== TEST TOKEN INFO ===")
    try:
        response = requests.get(f"{API_BASE_URL}/token-info")
        print(f"Status: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2)}")
        return response.status_code == 200
    except Exception as e:
        print(f"Error: {e}")
        return False

def test_list_invoices():
    """Test list invoices endpoint"""
    print("\n=== TEST LIST INVOICES ===")
    try:
        response = requests.get(f"{API_BASE_URL}/list-invoices")
        print(f"Status: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2)}")
        return response.status_code == 200
    except Exception as e:
        print(f"Error: {e}")
        return False

def test_create_invoice():
    """Test create invoice endpoint"""
    print("\n=== TEST CREATE INVOICE ===")
    try:
        # Đọc dữ liệu mẫu
        with open(SAMPLE_INVOICE_FILE, 'r', encoding='utf-8') as f:
            invoice_data = json.load(f)
        
        print(f"Sending invoice data: {len(str(invoice_data))} characters")
        
        # Gọi API
        response = requests.post(
            f"{API_BASE_URL}/create-sample-invoice",
            json=invoice_data,
            headers={'Content-Type': 'application/json'}
        )
        
        print(f"Status: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2)}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                return result.get('filename')
        
        return None
        
    except Exception as e:
        print(f"Error: {e}")
        return None

def test_view_invoice(filename):
    """Test view invoice endpoint"""
    print(f"\n=== TEST VIEW INVOICE: {filename} ===")
    try:
        response = requests.get(f"{API_BASE_URL}/view-invoice/{filename}")
        print(f"Status: {response.status_code}")
        print(f"Content-Type: {response.headers.get('content-type')}")
        print(f"Content-Length: {len(response.content)} bytes")
        
        if response.status_code == 200:
            # Kiểm tra xem có phải PDF không
            if response.content.startswith(b'%PDF'):
                print("✅ Valid PDF file")
                return True
            else:
                print("❌ Not a valid PDF file")
                return False
        
        return False
        
    except Exception as e:
        print(f"Error: {e}")
        return False

def test_invalid_requests():
    """Test các request không hợp lệ"""
    print("\n=== TEST INVALID REQUESTS ===")
    
    # Test 1: Empty JSON
    print("1. Test empty JSON:")
    try:
        response = requests.post(
            f"{API_BASE_URL}/create-sample-invoice",
            json={},
            headers={'Content-Type': 'application/json'}
        )
        print(f"   Status: {response.status_code}")
        print(f"   Response: {response.json()}")
    except Exception as e:
        print(f"   Error: {e}")
    
    # Test 2: Invalid JSON structure
    print("\n2. Test invalid JSON structure:")
    try:
        response = requests.post(
            f"{API_BASE_URL}/create-sample-invoice",
            json={"invalid": "data"},
            headers={'Content-Type': 'application/json'}
        )
        print(f"   Status: {response.status_code}")
        print(f"   Response: {response.json()}")
    except Exception as e:
        print(f"   Error: {e}")
    
    # Test 3: Non-existent PDF
    print("\n3. Test non-existent PDF:")
    try:
        response = requests.get(f"{API_BASE_URL}/view-invoice/nonexistent.pdf")
        print(f"   Status: {response.status_code}")
    except Exception as e:
        print(f"   Error: {e}")
    
    # Test 4: Invalid filename
    print("\n4. Test invalid filename:")
    try:
        response = requests.get(f"{API_BASE_URL}/view-invoice/../etc/passwd")
        print(f"   Status: {response.status_code}")
    except Exception as e:
        print(f"   Error: {e}")

def main():
    """Chạy tất cả tests"""
    print("🚀 BẮT ĐẦU TEST FPT EINVOICE API SERVER")
    print("="*60)
    
    # Kiểm tra server có chạy không
    print("Kiểm tra server...")
    try:
        response = requests.get(f"{API_BASE_URL}/health", timeout=5)
        if response.status_code != 200:
            print("❌ Server không phản hồi. Vui lòng khởi động server trước.")
            return
    except Exception as e:
        print(f"❌ Không thể kết nối đến server: {e}")
        print("Vui lòng chạy: python invoice_api_server.py")
        return
    
    print("✅ Server đang chạy")
    
    # Chạy các tests
    results = {}
    
    results['health_check'] = test_health_check()
    results['token_info'] = test_token_info()
    results['list_invoices'] = test_list_invoices()
    
    # Test tạo hóa đơn
    created_filename = test_create_invoice()
    results['create_invoice'] = created_filename is not None
    
    # Test xem hóa đơn nếu tạo thành công
    if created_filename:
        results['view_invoice'] = test_view_invoice(created_filename)
    else:
        results['view_invoice'] = False
    
    # Test các request không hợp lệ
    test_invalid_requests()
    
    # Tổng kết
    print("\n" + "="*60)
    print("📊 KẾT QUẢ TEST:")
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {test_name}: {status}")
    
    passed = sum(results.values())
    total = len(results)
    print(f"\nTổng kết: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 TẤT CẢ TESTS ĐỀU THÀNH CÔNG!")
    else:
        print("⚠️ Một số tests thất bại. Vui lòng kiểm tra lại.")
    
    print("\n💡 Để test thủ công:")
    print(f"   - Health check: GET {API_BASE_URL}/health")
    print(f"   - Token info: GET {API_BASE_URL}/token-info")
    print(f"   - List invoices: GET {API_BASE_URL}/list-invoices")
    print(f"   - Create invoice: POST {API_BASE_URL}/create-sample-invoice")
    if created_filename:
        print(f"   - View PDF: GET {API_BASE_URL}/view-invoice/{created_filename}")

if __name__ == "__main__":
    main()
