#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Minimal API để test Flask hoạt động
"""

from flask import Flask, jsonify, request, send_file, Response
import json
import os
import base64
import requests
from datetime import datetime
import io
import subprocess
import time
import logging
import threading
from threading import Lock, Event
from functools import wraps
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

app = Flask(__name__)

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Global variables for concurrent authentication management
_auth_lock = Lock()  # Đảm bảo chỉ 1 selenium process chạy
_auth_in_progress = False  # Flag để track authentication status
_auth_event = None  # Event để notify waiting threads
_auth_result = None  # Kết quả authentication để share cho waiting threads
_waiting_count = 0  # Số lượng requests đang wait

# Load API authentication key
API_AUTH_KEY = os.getenv('API_AUTH_KEY')
if not API_AUTH_KEY:
    logger.error("❌ API_AUTH_KEY not found in environment variables")
    logger.error("Please set API_AUTH_KEY in .env file")
    exit(1)
else:
    logger.info(f"✅ API authentication enabled (key length: {len(API_AUTH_KEY)})")

def require_api_key(f):
    """
    Decorator để require API key authentication
    """
    @wraps(f)
    def decorated_function(*args, **kwargs):
        # Kiểm tra Authorization header (Bearer token)
        auth_header = request.headers.get('Authorization')
        api_key_header = request.headers.get('X-API-Key')

        provided_key = None

        # Check Authorization: Bearer <key>
        if auth_header and auth_header.startswith('Bearer '):
            provided_key = auth_header[7:]  # Remove "Bearer " prefix
        # Check X-API-Key header
        elif api_key_header:
            provided_key = api_key_header

        if not provided_key:
            logger.warning("🔒 Authentication failed: Missing API key")
            return jsonify({
                'success': False,
                'error': 'Authentication required'
            }), 401

        if provided_key != API_AUTH_KEY:
            logger.warning(f"🔒 Authentication failed: Invalid API key (length: {len(provided_key)})")
            return jsonify({
                'success': False,
                'error': 'Invalid API key'
            }), 401

        logger.info(f"✅ Authentication successful for endpoint: {request.endpoint}")
        return f(*args, **kwargs)

    return decorated_function

def is_token_expired_error(status_code, response_text):
    """
    Kiểm tra xem lỗi có phải do token hết hạn không
    """
    if status_code == 500:
        try:
            response_data = json.loads(response_text)
            error_msg = response_data.get('error', '').lower()

            # Các dấu hiệu token hết hạn
            token_error_indicators = [
                'invalid_authentication_information',
                'authentication',
                'unauthorized',
                'token',
                'expired',
                'invalid token',
                'authentication failed'
            ]

            return any(indicator in error_msg for indicator in token_error_indicators)
        except:
            pass

    # HTTP 401 cũng là dấu hiệu token hết hạn
    return status_code == 401

def _run_selenium_process():
    """
    Internal function để chạy selenium process
    Chỉ được gọi bởi thread đầu tiên
    """
    try:
        logger.info("🔄 Bắt đầu quá trình re-authentication...")

        # Chạy selenium script
        result = subprocess.run(
            ['python', 'selenium_automation.py'],
            capture_output=True,
            text=True,
            timeout=120,  # Timeout 2 phút
            cwd=os.getcwd()
        )

        logger.info(f"Selenium script exit code: {result.returncode}")

        if result.returncode == 0:
            logger.info("✅ Selenium script hoàn thành thành công")
            logger.info(f"Output: {result.stdout[-200:]}")  # Log 200 ký tự cuối

            # Chờ một chút để file được ghi hoàn toàn
            time.sleep(2)

            # Kiểm tra xem token mới có được tạo không
            if os.path.exists('session_token.json'):
                with open('session_token.json', 'r', encoding='utf-8') as f:
                    token_data = json.load(f)

                new_token = token_data.get('token')
                if new_token and len(new_token) > 50:
                    logger.info(f"✅ Token mới đã được tạo (length: {len(new_token)})")
                    return True, "Token refreshed successfully"
                else:
                    logger.error("❌ Token mới không hợp lệ")
                    return False, "New token is invalid"
            else:
                logger.error("❌ File session_token.json không được tạo")
                return False, "Token file not created"
        else:
            logger.error(f"❌ Selenium script thất bại: {result.stderr}")
            return False, f"Selenium failed: {result.stderr[:200]}"

    except subprocess.TimeoutExpired:
        logger.error("❌ Selenium script timeout (>2 phút)")
        return False, "Authentication timeout"
    except Exception as e:
        logger.error(f"❌ Lỗi khi chạy selenium: {e}")
        return False, f"Selenium error: {str(e)}"

def run_selenium_authentication():
    """
    Thread-safe selenium authentication với concurrent request handling
    """
    global _auth_lock, _auth_in_progress, _auth_event, _auth_result, _waiting_count

    with _auth_lock:
        # Nếu đã có authentication đang chạy
        if _auth_in_progress:
            _waiting_count += 1
            current_event = _auth_event
            logger.info(f"⏳ Authentication đang chạy, waiting... (queue: {_waiting_count})")
        else:
            # Thread đầu tiên sẽ chạy authentication
            _auth_in_progress = True
            _auth_event = Event()
            _waiting_count = 0
            current_event = _auth_event
            logger.info("🚀 Bắt đầu authentication process (first thread)")

            # Chạy selenium trong thread hiện tại
            try:
                success, message = _run_selenium_process()
                _auth_result = (success, message)
                logger.info(f"🏁 Authentication hoàn thành: {success} - {message}")
            except Exception as e:
                _auth_result = (False, f"Authentication error: {str(e)}")
                logger.error(f"❌ Authentication exception: {e}")
            finally:
                # Notify tất cả waiting threads
                _auth_in_progress = False
                current_event.set()
                logger.info(f"📢 Notified {_waiting_count} waiting threads")
                _waiting_count = 0

            return _auth_result

    # Waiting threads sẽ đến đây
    logger.info("⏳ Waiting for authentication to complete...")

    # Wait với timeout 150 giây
    if current_event.wait(timeout=150):
        # Authentication hoàn thành
        with _auth_lock:
            _waiting_count = max(0, _waiting_count - 1)
            result = _auth_result

        logger.info(f"✅ Received authentication result: {result[0]} - {result[1]}")
        return result
    else:
        # Timeout
        with _auth_lock:
            _waiting_count = max(0, _waiting_count - 1)

        logger.error("❌ Authentication wait timeout (150s)")
        return False, "Authentication wait timeout"

def call_fpt_api_with_retry(invoice_data, max_retries=1):
    """
    Gọi FPT API với retry logic khi token hết hạn
    """
    for attempt in range(max_retries + 1):
        try:
            logger.info(f"🔄 FPT API attempt {attempt + 1}/{max_retries + 1}")

            # Load token
            if not os.path.exists('session_token.json'):
                return False, "Token file not found", None

            with open('session_token.json', 'r', encoding='utf-8') as f:
                token_data = json.load(f)

            token = token_data.get('token')
            if not token:
                return False, "No token found in file", None

            # Call FPT API
            url = 'https://portal.einvoice.fpt.com.vn/api/inv/pdf/view'
            headers = {
                'Authorization': f'Bearer {token}',
                'Content-Type': 'application/json'
            }

            logger.info(f"Calling FPT API: {url}")
            logger.info(f"Token length: {len(token)}")

            response = requests.post(url, json=invoice_data, headers=headers, timeout=30)

            logger.info(f"Response status: {response.status_code}")

            if response.status_code == 200:
                logger.info("✅ FPT API thành công")
                return True, "Success", response

            # Kiểm tra xem có phải lỗi token không
            elif is_token_expired_error(response.status_code, response.text):
                logger.warning(f"⚠️ Token expired error detected: {response.status_code}")
                logger.warning(f"Response: {response.text[:200]}")

                if attempt < max_retries:
                    logger.info("🔄 Attempting re-authentication...")

                    # Thử đăng nhập lại
                    auth_success, auth_message = run_selenium_authentication()

                    if auth_success:
                        logger.info("✅ Re-authentication thành công, retry API call...")
                        continue  # Retry với token mới
                    else:
                        logger.error(f"❌ Re-authentication thất bại: {auth_message}")
                        return False, f"Re-authentication failed: {auth_message}", None
                else:
                    logger.error("❌ Đã hết số lần retry")
                    return False, f"Token expired and re-authentication failed", None

            else:
                # Lỗi khác không liên quan đến token
                logger.error(f"❌ FPT API error: {response.status_code}")
                return False, f"FPT API error: {response.status_code} - {response.text}", None

        except Exception as e:
            logger.error(f"❌ Exception in API call: {e}")
            if attempt < max_retries:
                logger.info("🔄 Retrying after exception...")
                time.sleep(2)
                continue
            else:
                return False, f"API call failed: {str(e)}", None

    return False, "All retry attempts failed", None

@app.route('/', methods=['GET'])
def home():
    return jsonify({
        'message': 'FPT eInvoice API Server',
        'version': '1.0.0',
        'endpoints': [
            'GET /',
            'GET /health',
            'GET /token-info',
            'POST /create-sample-invoice',
            'POST /create-view-sample-invoice',
            'GET /view-invoice/<filename>'
        ]
    })

@app.route('/health', methods=['GET'])
def health():
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.now().isoformat(),
        'version': '1.0.0'
    })

@app.route('/token-info', methods=['GET'])
@require_api_key
def token_info():
    try:
        if os.path.exists('session_token.json'):
            with open('session_token.json', 'r', encoding='utf-8') as f:
                token_data = json.load(f)
            
            return jsonify({
                'success': True,
                'has_token': bool(token_data.get('token')),
                'token_length': len(token_data.get('token', '')),
                'token_type': token_data.get('token_type'),
                'token_source': token_data.get('token_source'),
                'extracted_at': token_data.get('extracted_at')
            })
        else:
            return jsonify({
                'success': False,
                'error': 'Token file not found'
            })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/create-sample-invoice', methods=['POST'])
@require_api_key
def create_sample_invoice():
    try:
        # Validate request
        if not request.is_json:
            return jsonify({
                'success': False,
                'error': 'Content-Type must be application/json'
            }), 400
        
        invoice_data = request.get_json()
        if not invoice_data:
            return jsonify({
                'success': False,
                'error': 'Request body cannot be empty'
            }), 400
        
        logger.info(f"Nhận được dữ liệu hóa đơn: {len(str(invoice_data))} characters")

        # Call FPT API with retry logic
        success, error_message, response = call_fpt_api_with_retry(invoice_data, max_retries=1)

        if not success:
            logger.error(f"FPT API failed: {error_message}")
            return jsonify({
                'success': False,
                'error': error_message
            }), 500

        logger.info("✅ FPT API thành công")

        if response.status_code == 200:
            # Create invoices directory
            os.makedirs('./invoices', exist_ok=True)
            
            # Generate filename
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"hdn_{timestamp}.pdf"
            filepath = os.path.join('./invoices', filename)
            
            # Save PDF
            pdf_data = None
            
            # Try different ways to get PDF data
            content_type = response.headers.get('content-type', '')
            
            if content_type.startswith('application/pdf'):
                # Binary PDF
                pdf_data = response.content
                print(f"Got binary PDF: {len(pdf_data)} bytes")
            else:
                # Try JSON response
                try:
                    response_json = response.json()
                    print(f"Response JSON keys: {list(response_json.keys())}")
                    
                    # Look for PDF data in common fields
                    for field in ['pdf', 'data', 'content', 'file', 'base64']:
                        if field in response_json:
                            pdf_data = response_json[field]
                            print(f"Found PDF data in field: {field}")
                            break
                    
                    if isinstance(pdf_data, str):
                        # Decode base64
                        if pdf_data.startswith('data:application/pdf;base64,'):
                            pdf_data = pdf_data[len('data:application/pdf;base64,'):]
                        
                        pdf_data = base64.b64decode(pdf_data)
                        print(f"Decoded base64 PDF: {len(pdf_data)} bytes")
                        
                except Exception as e:
                    print(f"Error parsing JSON: {e}")
                    return jsonify({
                        'success': False,
                        'error': f'Cannot parse response: {e}'
                    }), 500
            
            if pdf_data:
                # Save file
                with open(filepath, 'wb') as f:
                    f.write(pdf_data)
                
                print(f"Saved PDF: {filepath}")
                
                return jsonify({
                    'success': True,
                    'filename': filename,
                    'pdf_url': f'/view-invoice/{filename}',
                    'file_size': len(pdf_data),
                    'created_at': datetime.now().isoformat()
                })
            else:
                return jsonify({
                    'success': False,
                    'error': 'No PDF data found in response'
                }), 500
        else:
            # Không nên xảy ra vì đã được xử lý trong call_fpt_api_with_retry
            logger.error(f"Unexpected response status: {response.status_code}")
            return jsonify({
                'success': False,
                'error': f'Unexpected API response: {response.status_code}'
            }), 500

    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        return jsonify({
            'success': False,
            'error': f'Server error: {str(e)}'
        }), 500

@app.route('/view-invoice/<filename>', methods=['GET'])
@require_api_key
def view_invoice(filename):
    try:
        filepath = os.path.join('./invoices', filename)
        
        if not os.path.exists(filepath):
            return jsonify({
                'success': False,
                'error': 'File not found'
            }), 404
        
        return send_file(filepath, mimetype='application/pdf')
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/create-view-sample-invoice', methods=['POST'])
@require_api_key
def create_view_sample_invoice():
    """
    API endpoint tạo và hiển thị hóa đơn PDF trực tiếp
    Trả về PDF stream thay vì lưu file
    """
    try:
        print("=== BẮT ĐẦU TẠO VÀ HIỂN THỊ HÓA ĐƠN TRỰC TIẾP ===")

        # 1. Validate request
        if not request.is_json:
            return jsonify({
                'success': False,
                'error': 'Content-Type must be application/json'
            }), 400

        invoice_data = request.get_json()
        if not invoice_data:
            return jsonify({
                'success': False,
                'error': 'Request body cannot be empty'
            }), 400

        logger.info(f"Nhận được dữ liệu hóa đơn: {len(str(invoice_data))} characters")

        # 2. Call FPT API with retry logic
        success, error_message, response = call_fpt_api_with_retry(invoice_data, max_retries=1)

        if not success:
            logger.error(f"FPT API failed: {error_message}")
            return jsonify({
                'success': False,
                'error': error_message
            }), 500

        logger.info("✅ FPT API thành công")

        if response.status_code == 200:
            # 4. Process PDF data
            pdf_data = None

            # Try different ways to get PDF data
            content_type = response.headers.get('content-type', '')

            if content_type.startswith('application/pdf'):
                # Binary PDF
                pdf_data = response.content
                print(f"Got binary PDF: {len(pdf_data)} bytes")
            else:
                # Try JSON response
                try:
                    response_json = response.json()
                    print(f"Response JSON keys: {list(response_json.keys())}")

                    # Look for PDF data in common fields
                    for field in ['pdf', 'data', 'content', 'file', 'base64']:
                        if field in response_json:
                            pdf_data = response_json[field]
                            print(f"Found PDF data in field: {field}")
                            break

                    if isinstance(pdf_data, str):
                        # Decode base64
                        if pdf_data.startswith('data:application/pdf;base64,'):
                            pdf_data = pdf_data[len('data:application/pdf;base64,'):]

                        pdf_data = base64.b64decode(pdf_data)
                        print(f"Decoded base64 PDF: {len(pdf_data)} bytes")

                except Exception as e:
                    print(f"Error parsing JSON: {e}")
                    return jsonify({
                        'success': False,
                        'error': f'Cannot parse response: {e}'
                    }), 500

            if pdf_data:
                # 5. Return PDF directly as stream
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"hdn_{timestamp}.pdf"

                print(f"✅ Returning PDF directly: {filename} ({len(pdf_data)} bytes)")

                # Create BytesIO object for streaming
                pdf_buffer = io.BytesIO(pdf_data)
                pdf_buffer.seek(0)

                # Return PDF with proper headers for inline viewing
                return Response(
                    pdf_buffer.getvalue(),
                    mimetype='application/pdf',
                    headers={
                        'Content-Disposition': f'inline; filename="{filename}"',
                        'Content-Length': str(len(pdf_data)),
                        'Cache-Control': 'no-cache'
                    }
                )
            else:
                return jsonify({
                    'success': False,
                    'error': 'No PDF data found in response'
                }), 500
        else:
            # Không nên xảy ra vì đã được xử lý trong call_fpt_api_with_retry
            logger.error(f"Unexpected response status: {response.status_code}")
            return jsonify({
                'success': False,
                'error': f'Unexpected API response: {response.status_code}'
            }), 500

    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        return jsonify({
            'success': False,
            'error': f'Server error: {str(e)}'
        }), 500

if __name__ == '__main__':
    print("🚀 Starting FPT eInvoice API Server (Minimal Version)")
    print("📍 Server: http://localhost:5959")
    print("� Authentication: API Key Required")
    print("�🔍 Endpoints:")
    print("   - GET  / (public)")
    print("   - GET  /health (public)")
    print("   - GET  /token-info (🔒 auth required)")
    print("   - POST /create-sample-invoice (🔒 auth required)")
    print("   - POST /create-view-sample-invoice (🔒 auth required)")
    print("   - GET  /view-invoice/<filename> (🔒 auth required)")
    print("💡 Use Header: Authorization: Bearer <API_KEY>")
    print("💡 Or Header: X-API-Key: <API_KEY>")
    print("="*60)
    
    app.run(host='0.0.0.0', port=5959, debug=True)
