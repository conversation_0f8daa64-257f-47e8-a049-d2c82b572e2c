# -*- coding: utf-8 -*-
"""
KỊCH BẢN TỰ ĐỘNG ĐĂNG NHẬP VỚI SELENIUM VÀ RECAPTCHA V3
---------------------------------------------------------
Yêu cầu:
pip install selenium webdriver-manager selenium-stealth

Lưu ý:
- <PERSON><PERSON> thuật này phức tạp và có thể không ổn định nếu trang web thay đổi.
- Đ<PERSON> có độ tin cậy cao nhất trong môi trường production, hãy cân nhắc dùng dịch vụ giải CAPTCHA.
"""

import time
import random
import logging
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.service import Service as ChromeService
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException, WebDriverException
from webdriver_manager.chrome import ChromeDriverManager
from selenium_stealth import stealth

# Thiết lập logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# =====================================================================================
# <<< THAY ĐỔI CÁC GIÁ TRỊ NÀY CHO PHÙ HỢP VỚI TRANG WEB CỦA BẠN >>>
# =====================================================================================
LOGIN_URL = "https://portal.einvoice.fpt.com.vn/"  # Thay bằng URL trang đăng nhập thực tế
MASOTHUE = "0106512255"
USERNAME = "hhuong"             # Thay bằng username của bạn
PASSWORD = "eb6e8a09"                 # Thay bằng password của bạn

# --- Cấu hình Selector (quan trọng nhất!) ---
# Sử dụng By.ID, By.NAME, By.XPATH, hoặc By.CLASS_NAME...
# Ví dụ: (By.ID, "user-name"), (By.NAME, "password"), (By.XPATH, "//button[@type='submit']")
# Thứ tự điền: Mã số thuế -> Username -> Password -> Click đăng nhập
MASOTHUE_SELECTOR = (By.XPATH, "/html/body/div[3]/div[2]/div[2]/div[2]/div/div[3]/div/input")  # Ô mã số thuế
USERNAME_SELECTOR = (By.XPATH, "/html/body/div[3]/div[2]/div[2]/div[2]/div/div[4]/div/input")  # Ô tên đăng nhập
PASSWORD_SELECTOR = (By.XPATH, "/html/body/div[3]/div[2]/div[2]/div[2]/div/div[5]/div/input")  # Ô mật khẩu
LOGIN_BUTTON_SELECTOR = (By.XPATH, "/html/body/div[3]/div[2]/div[2]/div[2]/div/div[6]/div[1]/div/button")  # Nút đăng nhập

# --- Selector để xác nhận đăng nhập thành công ---
# Tìm một element chỉ xuất hiện SAU KHI đăng nhập thành công (ví dụ: avatar, dashboard...)
SUCCESS_ELEMENT_SELECTOR = (By.CLASS_NAME, "webix_disabled_view")
# =====================================================================================


def natural_typing(element, text: str):
    """Mô phỏng hành vi gõ phím tự nhiên của người dùng."""
    try:
        element.clear()  # Xóa nội dung cũ trước khi gõ
        for char in text:
            element.send_keys(char)
            time.sleep(random.uniform(0.05, 0.15))
    except Exception as e:
        logger.error(f"Lỗi khi gõ text '{text}': {e}")
        raise

def wait_for_recaptcha_v3(driver, wait, max_attempts=5):
    """
    Chờ reCAPTCHA v3 load và lấy token.
    reCAPTCHA v3 hoạt động khác v2, không có checkbox mà chạy ngầm.
    """
    logger.info("Đang chờ reCAPTCHA v3 khởi tạo...")

    for attempt in range(max_attempts):
        try:
            logger.info(f"Lần thử {attempt + 1}/{max_attempts}")

            # Chờ script reCAPTCHA load
            time.sleep(random.uniform(2, 4))

            # Kiểm tra xem reCAPTCHA script đã load chưa
            recaptcha_script_loaded = driver.execute_script("""
                return typeof grecaptcha !== 'undefined' &&
                       typeof grecaptcha.ready !== 'undefined';
            """)

            if not recaptcha_script_loaded:
                logger.warning(f"reCAPTCHA script chưa load (attempt {attempt + 1})")
                time.sleep(2)
                continue

            # Trigger reCAPTCHA v3 execution
            logger.info("Triggering reCAPTCHA v3...")
            driver.execute_script("""
                if (typeof grecaptcha !== 'undefined' && grecaptcha.ready) {
                    grecaptcha.ready(function() {
                        console.log('reCAPTCHA ready');
                    });
                }
            """)

            # Chờ một chút để reCAPTCHA xử lý
            time.sleep(random.uniform(3, 5))

            # Thử tìm token trong các element có thể
            token_selectors = [
                (By.ID, "g-recaptcha-response"),
                (By.NAME, "g-recaptcha-response"),
                (By.CSS_SELECTOR, "[name='g-recaptcha-response']"),
                (By.CSS_SELECTOR, "textarea[name='g-recaptcha-response']"),
                (By.XPATH, "//textarea[@name='g-recaptcha-response']")
            ]

            for selector in token_selectors:
                try:
                    token_element = driver.find_element(*selector)
                    token_value = token_element.get_attribute("value")

                    if token_value and len(token_value) > 10:
                        logger.info(f"✅ Tìm thấy reCAPTCHA token! (Length: {len(token_value)})")
                        return token_value
                    else:
                        logger.debug(f"Token element tìm thấy nhưng chưa có giá trị: {selector}")

                except NoSuchElementException:
                    logger.debug(f"Không tìm thấy element với selector: {selector}")
                    continue

            logger.warning(f"Không tìm thấy token hợp lệ (attempt {attempt + 1})")

            # Thử click vào trang để trigger reCAPTCHA
            if attempt < max_attempts - 1:
                logger.info("Thử click vào trang để trigger reCAPTCHA...")
                driver.execute_script("document.body.click();")
                time.sleep(2)

        except Exception as e:
            logger.error(f"Lỗi trong attempt {attempt + 1}: {e}")
            if attempt < max_attempts - 1:
                time.sleep(2)
                continue
            else:
                raise

    logger.warning("Không thể lấy reCAPTCHA token sau tất cả attempts")
    return None

def safe_find_element(driver, wait, selector, timeout=10, element_name="element"):
    """Tìm element một cách an toàn với error handling."""
    try:
        logger.info(f"Đang tìm {element_name}...")
        element = wait.until(EC.presence_of_element_located(selector))
        logger.info(f"✅ Tìm thấy {element_name}")
        return element
    except TimeoutException:
        logger.error(f"❌ Timeout khi tìm {element_name} sau {timeout}s")
        # Thử tìm bằng cách khác
        try:
            element = driver.find_element(*selector)
            logger.info(f"✅ Tìm thấy {element_name} bằng find_element")
            return element
        except NoSuchElementException:
            logger.error(f"❌ Không tìm thấy {element_name} bằng bất kỳ cách nào")
            raise
    except Exception as e:
        logger.error(f"❌ Lỗi không mong muốn khi tìm {element_name}: {e}")
        raise

def safe_click_element(driver, wait, selector, timeout=10, element_name="element"):
    """Click element một cách an toàn với error handling."""
    try:
        logger.info(f"Đang chờ {element_name} có thể click...")
        element = wait.until(EC.element_to_be_clickable(selector))

        # Scroll đến element trước khi click
        driver.execute_script("arguments[0].scrollIntoView(true);", element)
        time.sleep(0.5)

        # Thử click bằng Selenium
        try:
            element.click()
            logger.info(f"✅ Đã click {element_name} bằng Selenium")
            return True
        except Exception as e:
            logger.warning(f"Click Selenium thất bại: {e}, thử JavaScript...")
            # Fallback: click bằng JavaScript
            driver.execute_script("arguments[0].click();", element)
            logger.info(f"✅ Đã click {element_name} bằng JavaScript")
            return True

    except TimeoutException:
        logger.error(f"❌ Timeout khi chờ {element_name} có thể click sau {timeout}s")
        return False
    except Exception as e:
        logger.error(f"❌ Lỗi khi click {element_name}: {e}")
        return False

def setup_chrome_driver():
    """Thiết lập Chrome driver với các options tối ưu cho stability."""
    logger.info("Đang thiết lập Chrome driver...")

    options = webdriver.ChromeOptions()

    # Basic options
    options.add_argument("--start-maximized")
    options.add_argument("--no-sandbox")
    options.add_argument("--disable-dev-shm-usage")
    options.add_argument("--disable-gpu")
    options.add_argument("--disable-web-security")
    options.add_argument("--allow-running-insecure-content")

    # Disable automation detection
    options.add_experimental_option("excludeSwitches", ["enable-automation"])
    options.add_experimental_option('useAutomationExtension', False)
    options.add_argument("--disable-blink-features=AutomationControlled")

    # Disable logging để giảm TensorFlow warnings
    options.add_argument("--disable-logging")
    options.add_argument("--log-level=3")
    options.add_argument("--silent")
    options.add_experimental_option('excludeSwitches', ['enable-logging'])
    options.add_experimental_option('useAutomationExtension', False)

    # Performance improvements
    options.add_argument("--disable-extensions")
    options.add_argument("--disable-plugins")
    options.add_argument("--disable-images")
    options.add_argument("--disable-javascript-harmony-shipping")

    # User agent
    options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")

    # Prefs để disable notifications và các popup
    prefs = {
        "profile.default_content_setting_values": {
            "notifications": 2,
            "media_stream": 2,
        },
        "profile.managed_default_content_settings": {
            "images": 2
        }
    }
    options.add_experimental_option("prefs", prefs)

    try:
        service = ChromeService(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=options)
        logger.info("✅ Chrome driver đã được thiết lập thành công")
        return driver
    except Exception as e:
        logger.error(f"❌ Lỗi khi thiết lập Chrome driver: {e}")
        raise

def main():
    """Hàm chính thực thi toàn bộ luồng đăng nhập."""
    driver = None

    try:
        # Thiết lập driver
        driver = setup_chrome_driver()

        # Áp dụng "tàng hình" để tránh bị phát hiện
        logger.info("Áp dụng stealth mode...")
        stealth(driver,
                languages=["en-US", "en"],
                vendor="Google Inc.",
                platform="Win32",
                webgl_vendor="Intel Inc.",
                renderer="Intel Iris OpenGL Engine",
                fix_hairline=True,
                )

        logger.info(">>> Bắt đầu quá trình đăng nhập tự động...")
        driver.get(LOGIN_URL)
        wait = WebDriverWait(driver, 30)  # Tăng timeout lên 30s

        logger.info(f"1. Đã truy cập trang: {LOGIN_URL}")
        time.sleep(random.uniform(3, 5))  # Chờ lâu hơn để trang load hoàn toàn

        # 2. Tìm và điền thông tin đăng nhập với error handling
        logger.info("2. Đang tìm các ô input...")

        masothue_field = safe_find_element(driver, wait, MASOTHUE_SELECTOR,
                                         element_name="ô mã số thuế")
        username_field = safe_find_element(driver, wait, USERNAME_SELECTOR,
                                         element_name="ô username")
        password_field = safe_find_element(driver, wait, PASSWORD_SELECTOR,
                                         element_name="ô password")

        logger.info("✅ Đã tìm thấy tất cả ô input. Bắt đầu điền thông tin...")

        # Điền thông tin với error handling
        logger.info(f"   -> Điền mã số thuế: {MASOTHUE}")
        natural_typing(masothue_field, MASOTHUE)
        time.sleep(random.uniform(0.5, 1))

        logger.info(f"   -> Điền username: {USERNAME}")
        natural_typing(username_field, USERNAME)
        time.sleep(random.uniform(0.5, 1))

        logger.info("   -> Điền password: ********")
        natural_typing(password_field, PASSWORD)

        # 3. Mô phỏng hành vi người dùng
        logger.info("3. Mô phỏng hành vi người dùng...")
        driver.execute_script("window.scrollTo(0, document.body.scrollHeight / 4);")
        time.sleep(random.uniform(1, 2))

        # Di chuyển chuột ngẫu nhiên
        driver.execute_script("""
            var event = new MouseEvent('mousemove', {
                'view': window,
                'bubbles': true,
                'cancelable': true,
                'clientX': Math.random() * window.innerWidth,
                'clientY': Math.random() * window.innerHeight
            });
            document.dispatchEvent(event);
        """)
        time.sleep(random.uniform(1, 2))

        # 4. Xử lý reCAPTCHA v3 với error handling cải thiện
        logger.info("4. Đang xử lý reCAPTCHA v3...")
        recaptcha_token = wait_for_recaptcha_v3(driver, wait, max_attempts=3)

        if recaptcha_token:
            logger.info(f"✅ Lấy reCAPTCHA token thành công! (Length: {len(recaptcha_token)})")
        else:
            logger.warning("⚠️ Không lấy được reCAPTCHA token, tiếp tục thử đăng nhập...")

        # 5. Click nút đăng nhập với error handling
        logger.info("5. Đang click nút đăng nhập...")
        click_success = safe_click_element(driver, wait, LOGIN_BUTTON_SELECTOR,
                                         element_name="nút đăng nhập")

        if not click_success:
            raise Exception("Không thể click nút đăng nhập")

        logger.info("✅ Đã click nút đăng nhập")

        # 6. Chờ và xác nhận kết quả
        logger.info("6. Đang chờ kết quả đăng nhập...")
        time.sleep(random.uniform(3, 5))

        try:
            # Thử tìm element thành công
            success_element = wait.until(EC.presence_of_element_located(SUCCESS_ELEMENT_SELECTOR))

            logger.info("\n" + "="*50)
            logger.info("✅ ĐĂNG NHẬP THÀNH CÔNG!")
            logger.info(f"URL hiện tại: {driver.current_url}")
            logger.info("="*50)

            # Giữ trình duyệt để xem kết quả
            time.sleep(10)

        except TimeoutException:
            # Kiểm tra xem có lỗi đăng nhập không
            current_url = driver.current_url
            page_source = driver.page_source

            logger.warning(f"Không tìm thấy element thành công. URL hiện tại: {current_url}")

            # Kiểm tra các dấu hiệu lỗi
            error_indicators = [
                "error", "invalid", "incorrect", "failed", "sai", "lỗi"
            ]

            page_text = page_source.lower()
            found_errors = [indicator for indicator in error_indicators if indicator in page_text]

            if found_errors:
                logger.error(f"❌ Phát hiện lỗi đăng nhập: {found_errors}")
            else:
                logger.info("⚠️ Không rõ trạng thái đăng nhập, có thể cần kiểm tra thủ công")

            # Chụp ảnh để debug
            screenshot_path = f"login_result_{int(time.time())}.png"
            driver.save_screenshot(screenshot_path)
            logger.info(f"Đã lưu screenshot: {screenshot_path}")

    except WebDriverException as e:
        logger.error(f"❌ Lỗi WebDriver: {e}")
        if driver:
            try:
                screenshot_path = f"webdriver_error_{int(time.time())}.png"
                driver.save_screenshot(screenshot_path)
                logger.info(f"Đã lưu screenshot lỗi: {screenshot_path}")
            except:
                pass
    except TimeoutException as e:
        logger.error(f"❌ Timeout: {e}")
        if driver:
            try:
                screenshot_path = f"timeout_error_{int(time.time())}.png"
                driver.save_screenshot(screenshot_path)
                logger.info(f"Đã lưu screenshot timeout: {screenshot_path}")
            except:
                pass
    except Exception as e:
        logger.error(f"❌ Lỗi không mong muốn: {e}")
        if driver:
            try:
                screenshot_path = f"general_error_{int(time.time())}.png"
                driver.save_screenshot(screenshot_path)
                logger.info(f"Đã lưu screenshot lỗi: {screenshot_path}")
            except:
                pass
    finally:
        if driver:
            logger.info(">>> Đóng trình duyệt...")
            try:
                driver.quit()
            except:
                pass
        logger.info(">>> Hoàn thành!")

if __name__ == "__main__":
    try:
        logger.info("🚀 Khởi động FPT eInvoice Selenium Automation")
        logger.info("="*60)
        main()
    except KeyboardInterrupt:
        logger.info("❌ Người dùng dừng chương trình")
    except Exception as e:
        logger.error(f"❌ Lỗi nghiêm trọng: {e}")
    finally:
        logger.info("🏁 Kết thúc chương trình")