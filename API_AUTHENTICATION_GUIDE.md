# API Authentication Guide

## 🎯 Tổng quan

FPT eInvoice API Server sử dụng **API Key Authentication** để bảo vệ các endpoints quan trọng khỏi unauthorized access.

## 🔐 Authentication Methods

### 1. Authorization Bearer Header
```bash
curl -H "Authorization: Bearer <API_KEY>" http://localhost:5959/token-info
```

### 2. X-API-Key Header
```bash
curl -H "X-API-Key: <API_KEY>" http://localhost:5959/token-info
```

## 📋 Endpoint Protection

### Public Endpoints (No Auth Required):
- ✅ `GET /` - Home page
- ✅ `GET /health` - Health check

### Protected Endpoints (Auth Required):
- 🔒 `GET /token-info` - Token information
- 🔒 `POST /create-sample-invoice` - Tạo hóa đơn và lưu file
- 🔒 `POST /create-view-sample-invoice` - Tạo và xem hóa đơn trực tiếp
- 🔒 `GET /view-invoice/<filename>` - Xem PDF đã lưu

## ⚙️ Configuration

### 1. Environment Setup
Thêm API key vào file `.env`:

```env
# API Authentication
API_AUTH_KEY=your_secret_api_key_here
```

### 2. Template File
File `.env.example` chứa template:

```env
# API Authentication
# Thay đổi key này trong production
API_AUTH_KEY=your_secret_api_key_here
```

### 3. Server Startup
Server sẽ load API key khi khởi động:

```
✅ API authentication enabled (key length: 32)
🔒 Authentication: API Key Required
```

## 🛡️ Security Implementation

### Decorator Function:
```python
@require_api_key
def protected_endpoint():
    # Endpoint logic here
    pass
```

### Authentication Flow:
```python
def require_api_key(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        # 1. Check Authorization: Bearer <key>
        auth_header = request.headers.get('Authorization')
        if auth_header and auth_header.startswith('Bearer '):
            provided_key = auth_header[7:]
        
        # 2. Check X-API-Key: <key>
        elif request.headers.get('X-API-Key'):
            provided_key = request.headers.get('X-API-Key')
        
        # 3. Validate key
        if provided_key != API_AUTH_KEY:
            return 401 Unauthorized
        
        # 4. Execute endpoint
        return f(*args, **kwargs)
```

## 📊 Error Responses

### Missing Authentication:
```json
{
  "success": false,
  "error": "Authentication required"
}
```
**HTTP Status:** 401 Unauthorized

### Invalid API Key:
```json
{
  "success": false,
  "error": "Invalid API key"
}
```
**HTTP Status:** 401 Unauthorized

## 💻 Usage Examples

### 1. cURL Examples:

#### Bearer Token:
```bash
# Get token info
curl -H "Authorization: Bearer fpt_einvoice_api_secret_key_2025" \
     http://localhost:5959/token-info

# Create invoice
curl -X POST \
     -H "Authorization: Bearer fpt_einvoice_api_secret_key_2025" \
     -H "Content-Type: application/json" \
     -d @sample_invoice.json \
     http://localhost:5959/create-sample-invoice
```

#### X-API-Key Header:
```bash
# Get token info
curl -H "X-API-Key: fpt_einvoice_api_secret_key_2025" \
     http://localhost:5959/token-info

# Create invoice
curl -X POST \
     -H "X-API-Key: fpt_einvoice_api_secret_key_2025" \
     -H "Content-Type: application/json" \
     -d @sample_invoice.json \
     http://localhost:5959/create-sample-invoice
```

### 2. Python Examples:

#### Using requests library:
```python
import requests
import os

# Load API key from environment
api_key = os.getenv('API_AUTH_KEY')

# Method 1: Bearer token
headers = {'Authorization': f'Bearer {api_key}'}
response = requests.get('http://localhost:5959/token-info', headers=headers)

# Method 2: X-API-Key header
headers = {'X-API-Key': api_key}
response = requests.get('http://localhost:5959/token-info', headers=headers)

# Create invoice with authentication
invoice_data = {...}  # Your invoice data
response = requests.post(
    'http://localhost:5959/create-sample-invoice',
    headers={'Authorization': f'Bearer {api_key}'},
    json=invoice_data
)
```

### 3. JavaScript Examples:

#### Using fetch API:
```javascript
const apiKey = 'fpt_einvoice_api_secret_key_2025';

// Method 1: Bearer token
fetch('http://localhost:5959/token-info', {
    headers: {
        'Authorization': `Bearer ${apiKey}`
    }
})
.then(response => response.json())
.then(data => console.log(data));

// Method 2: X-API-Key header
fetch('http://localhost:5959/token-info', {
    headers: {
        'X-API-Key': apiKey
    }
})
.then(response => response.json())
.then(data => console.log(data));

// Create invoice
fetch('http://localhost:5959/create-sample-invoice', {
    method: 'POST',
    headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
    },
    body: JSON.stringify(invoiceData)
})
.then(response => response.json())
.then(data => console.log(data));
```

## 🔍 Logging & Monitoring

### Authentication Logs:
```
✅ Authentication successful for endpoint: token_info
🔒 Authentication failed: Missing API key
🔒 Authentication failed: Invalid API key (length: 10)
```

### Security Features:
- ✅ **No key logging** - Actual key values không được log
- ✅ **Length logging** - Chỉ log độ dài key để debug
- ✅ **Endpoint tracking** - Log endpoint nào được access
- ✅ **Failure tracking** - Log authentication failures

## 🚨 Security Best Practices

### 1. API Key Management:
- 🔐 **Strong keys** - Sử dụng key dài và phức tạp
- 🔄 **Regular rotation** - Thay đổi key định kỳ
- 🚫 **No hardcoding** - Không hardcode key trong source code
- 📝 **Environment only** - Chỉ lưu trong .env file

### 2. Production Deployment:
- 🌐 **HTTPS only** - Luôn sử dụng HTTPS trong production
- 🔒 **Secure storage** - Lưu key trong secure environment
- 📊 **Monitor access** - Track API usage và authentication failures
- 🚨 **Alert system** - Cảnh báo khi có unauthorized attempts

### 3. Development:
- 🧪 **Test keys** - Sử dụng key khác nhau cho dev/staging/prod
- 📋 **Documentation** - Document API key requirements
- 🔍 **Validation** - Test authentication thoroughly
- 🚫 **Git ignore** - Đảm bảo .env không được commit

## 🔧 Troubleshooting

### Common Issues:

#### 1. "Authentication required" error:
```bash
# Check if you're including the header
curl -v http://localhost:5959/token-info
# Should include: Authorization: Bearer <key> or X-API-Key: <key>
```

#### 2. "Invalid API key" error:
```bash
# Check your .env file
cat .env | grep API_AUTH_KEY

# Check environment loading
python -c "import os; from dotenv import load_dotenv; load_dotenv(); print(os.getenv('API_AUTH_KEY'))"
```

#### 3. Server startup error:
```bash
# Check if API_AUTH_KEY is set
python -c "import os; print('API_AUTH_KEY' in os.environ)"
```

---

## 📞 Support

Nếu gặp vấn đề với API Authentication:

1. **Check .env file** - Đảm bảo API_AUTH_KEY được set
2. **Verify headers** - Kiểm tra Authorization hoặc X-API-Key header
3. **Test with curl** - Sử dụng curl để test authentication
4. **Check logs** - Xem server logs để debug

**API Authentication giúp bảo vệ FPT eInvoice API khỏi unauthorized access!** 🛡️
