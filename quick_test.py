#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Quick test để kiểm tra API
"""

import requests
import json
import time

def test_api():
    base_url = "http://localhost:5000"
    
    print("🧪 Testing API endpoints...")
    
    # Test 1: Health check
    try:
        print("\n1. Testing /health...")
        response = requests.get(f"{base_url}/health", timeout=5)
        print(f"   Status: {response.status_code}")
        print(f"   Response: {response.text[:200]}...")
        
        if response.status_code == 200:
            print("   ✅ Health check OK")
        else:
            print("   ❌ Health check failed")
            
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    # Test 2: Token info
    try:
        print("\n2. Testing /token-info...")
        response = requests.get(f"{base_url}/token-info", timeout=5)
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   Has token: {data.get('has_token')}")
            print(f"   Token type: {data.get('token_type')}")
            print("   ✅ Token info OK")
        else:
            print(f"   Response: {response.text}")
            
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    # Test 3: Create invoice (with sample data)
    try:
        print("\n3. Testing /create-sample-invoice...")
        
        # Simple test data
        test_data = {
            "buyer": {
                "name": "Test Company",
                "address": "Test Address"
            },
            "seller": {
                "name": "Seller Company", 
                "address": "Seller Address",
                "tax_code": "0123456789"
            },
            "items": [
                {
                    "name": "Test Item",
                    "quantity": 1,
                    "unit_price": 100000
                }
            ],
            "amounts": {
                "subtotal": 100000,
                "tax_amount": 10000,
                "total": 110000
            }
        }
        
        response = requests.post(
            f"{base_url}/create-sample-invoice",
            json=test_data,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print(f"   ✅ Invoice created: {data.get('filename')}")
                return data.get('filename')
            else:
                print(f"   ❌ Failed: {data.get('error')}")
        else:
            print(f"   Response: {response.text[:200]}...")
            
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    return None

if __name__ == "__main__":
    print("🚀 Quick API Test")
    print("="*40)
    
    # Wait a bit for server to start
    print("Waiting for server to start...")
    time.sleep(2)
    
    filename = test_api()
    
    if filename:
        print(f"\n🎉 Test completed! Created file: {filename}")
        print(f"📄 View at: http://localhost:5000/view-invoice/{filename}")
    else:
        print("\n⚠️ Some tests failed")
    
    print("\n💡 Manual test URLs:")
    print("   http://localhost:5000/")
    print("   http://localhost:5000/health")
    print("   http://localhost:5000/token-info")
