#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Simple API test để kiểm tra Flask hoạt động
"""

from flask import Flask, jsonify
import json
import os

app = Flask(__name__)

@app.route('/health', methods=['GET'])
def health():
    return jsonify({'status': 'ok', 'message': 'Server is running'})

@app.route('/token-test', methods=['GET'])
def token_test():
    try:
        if os.path.exists('session_token.json'):
            with open('session_token.json', 'r', encoding='utf-8') as f:
                token_data = json.load(f)
            return jsonify({
                'success': True,
                'has_token': bool(token_data.get('token')),
                'token_length': len(token_data.get('token', '')),
                'token_type': token_data.get('token_type')
            })
        else:
            return jsonify({
                'success': False,
                'error': 'Token file not found'
            })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

if __name__ == '__main__':
    print("🚀 Starting simple API test server...")
    print("📍 Server will run at: http://localhost:5000")
    print("🔍 Test endpoints:")
    print("   - GET /health")
    print("   - GET /token-test")
    print("="*50)
    
    app.run(
        host='0.0.0.0',
        port=5000,
        debug=True
    )
