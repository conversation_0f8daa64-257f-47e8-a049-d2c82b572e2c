# FPT eInvoice Integration Project

Hệ thống tích hợp với FPT eInvoice để tự động đăng nhập, trích xuất token và tạo hóa đơn PDF.

## 🚀 Tính năng chính

- ✅ **Đăng nhập tự động** vào FPT eInvoice portal bằng Selenium
- ✅ **Trích xuất JWT token** từ browser session storage
- ✅ **API Server** để tạo hóa đơn PDF từ JSON data
- ✅ **Xem PDF** trực tiếp trong browser
- ✅ **Quản lý token** tự động

## 📁 Cấu trúc project

```
├── selenium_automation.py    # Đăng nhập tự động và trích xuất token
├── api_server.py            # API server tạo hóa đơn PDF
├── pdf_viewer.py            # Hiển thị PDF từ response.json
├── session_token.json       # JWT token (tự động tạo)
├── sample_invoice.json      # Dữ liệu hóa đơn mẫu
├── requirements.txt         # Python dependencies
├── invoices/               # Thư mục lưu PDF (tự động tạo)
├── sample/                 # Dữ liệu mẫu từ FPT
└── README.md               # Tài liệu này
```

## 🔧 Cài đặt

### 1. Cài đặt dependencies

```bash
pip install -r requirements.txt
```

### 2. Cấu hình thông tin đăng nhập

Tạo file `.env` từ template:

```bash
cp .env.example .env
```

Chỉnh sửa file `.env` với thông tin thực tế:

```env
# Authentication Credentials
FPT_MASOTHUE=your_tax_code_here      # Mã số thuế
FPT_USERNAME=your_username_here      # Tên đăng nhập
FPT_PASSWORD=your_password_here      # Mật khẩu
```

**⚠️ Lưu ý:** File `.env` chứa thông tin nhạy cảm và đã được thêm vào `.gitignore`

### 3. Cấu hình API Authentication

Thêm API key vào file `.env`:

```env
# API Authentication
API_AUTH_KEY=your_secret_api_key_here
```

**🔒 Bảo mật:** Thay đổi API key này trong production và không chia sẻ với người khác.

## 📖 Cách sử dụng

### Bước 1: Đăng nhập và lấy token

```bash
python selenium_automation.py
```

**Kết quả:**
- Tự động đăng nhập vào FPT eInvoice portal
- Trích xuất JWT token từ session storage
- Lưu token vào file `session_token.json`

### Bước 2: Khởi động API server

```bash
python api_server.py
```

**Server chạy tại:** `http://localhost:5959`

### Bước 3: Tạo hóa đơn PDF

#### Cách 1: Lưu file và xem sau
```bash
curl -X POST http://localhost:5959/create-sample-invoice \
  -H "Authorization: Bearer <API_KEY>" \
  -H "Content-Type: application/json" \
  -d @sample_invoice.json
```

#### Cách 2: Xem trực tiếp (Recommended)
```bash
curl -X POST http://localhost:5959/create-view-sample-invoice \
  -H "Authorization: Bearer <API_KEY>" \
  -H "Content-Type: application/json" \
  -d @sample_invoice.json \
  --output invoice.pdf
```

#### Sử dụng Python:
```python
import requests
import json
import os

# Lấy API key từ environment
api_key = os.getenv('API_AUTH_KEY')
headers = {'Authorization': f'Bearer {api_key}'}

with open('sample_invoice.json', 'r') as f:
    data = json.load(f)

# Cách 1: Lưu file
response = requests.post(
    'http://localhost:5959/create-sample-invoice',
    headers=headers,
    json=data
)
result = response.json()
print(f"PDF URL: {result['pdf_url']}")

# Cách 2: Nhận PDF trực tiếp
response = requests.post(
    'http://localhost:5959/create-view-sample-invoice',
    headers=headers,
    json=data
)
with open('invoice.pdf', 'wb') as f:
    f.write(response.content)
```

### Bước 4: Xem PDF

- **Trực tiếp:** Gọi `/create-view-sample-invoice` và mở trong browser
- **Từ file:** Truy cập `http://localhost:5959/view-invoice/{filename}`

## 🌐 API Endpoints

### 🔒 Authentication Required

Tất cả API endpoints (trừ `/` và `/health`) yêu cầu API key authentication:

```bash
# Sử dụng Authorization Bearer header
curl -H "Authorization: Bearer <API_KEY>" http://localhost:5959/token-info

# Hoặc sử dụng X-API-Key header
curl -H "X-API-Key: <API_KEY>" http://localhost:5959/token-info
```

### 1. Health Check (Public)
```http
GET /health
```

### 2. Token Info (🔒 Auth Required)
```http
GET /token-info
Authorization: Bearer <API_KEY>
```

### 3. Tạo hóa đơn (lưu file) (🔒 Auth Required)
```http
POST /create-sample-invoice
Authorization: Bearer <API_KEY>
Content-Type: application/json

{
  "buyer": {...},
  "seller": {...},
  "items": [...],
  "amounts": {...}
}
```

### 4. Tạo và xem hóa đơn trực tiếp (🔒 Auth Required)
```http
POST /create-view-sample-invoice
Authorization: Bearer <API_KEY>
Content-Type: application/json

{
  "buyer": {...},
  "seller": {...},
  "items": [...],
  "amounts": {...}
}
```
**Response:** PDF file stream (hiển thị trực tiếp trong browser)

### 5. Xem PDF đã lưu (🔒 Auth Required)
```http
GET /view-invoice/{filename}
Authorization: Bearer <API_KEY>
```

## 📋 Dữ liệu hóa đơn mẫu

File `sample_invoice.json` chứa cấu trúc dữ liệu hoàn chỉnh:

```json
{
  "buyer": {
    "name": "CÔNG TY TNHH ABC",
    "address": "123 Đường ABC, TP.HCM",
    "tax_code": "0123456789"
  },
  "seller": {
    "name": "CÔNG TY TNHH XYZ", 
    "address": "456 Đường XYZ, Hà Nội",
    "tax_code": "0987654321"
  },
  "items": [
    {
      "name": "Dịch vụ kho bãi",
      "quantity": 1,
      "unit_price": 5000000,
      "tax_rate": 10
    }
  ],
  "amounts": {
    "subtotal": 7500000,
    "tax_amount": 750000,
    "total": 8250000
  }
}
```

## 🔒 Bảo mật & Auto Re-Authentication

- ✅ **API Key Authentication** - Bảo vệ tất cả endpoints quan trọng
- ✅ **JWT Token** được lưu trữ cục bộ
- ✅ **Thông tin đăng nhập** không được commit vào git
- ✅ **File validation** để tránh path traversal
- ✅ **Auto Re-Authentication** khi token hết hạn
- ✅ **Retry Logic** tự động với error handling thông minh
- ✅ **Token Expiry Detection** - tự động phát hiện lỗi 401/500
- ✅ **Selenium Auto-Run** - tự động chạy script đăng nhập khi cần
- ✅ **Authorization Headers** - Hỗ trợ Bearer token và X-API-Key

## 🤖 Auto Re-Authentication

### Tính năng tự động
API server có khả năng **tự động đăng nhập lại** khi token hết hạn:

1. **Phát hiện token hết hạn** - Khi nhận HTTP 401/500 từ FPT API
2. **Tự động chạy Selenium** - Gọi `python selenium_automation.py`
3. **Lấy token mới** - Cập nhật file `session_token.json`
4. **Retry API call** - Thử lại với token mới
5. **Trả về kết quả** - Hoạt động như bình thường

### Workflow tự động:
```
API Call → Token Expired → Auto Login → New Token → Retry → Success
```

### Concurrent Request Handling:
```
Request 1 → Runs Selenium → Gets Token → Success
Request 2 → Waits for R1 → Uses Token → Success
Request 3 → Waits for R1 → Uses Token → Success
```

**⚡ Lợi ích:**
- Không cần manual refresh token
- API hoạt động liên tục 24/7
- Xử lý concurrent requests hiệu quả
- Thread-safe authentication
- Không có race conditions
- Logging chi tiết

## 🐛 Troubleshooting

### Lỗi "Không thể load token"
```bash
# API sẽ tự động chạy selenium, nhưng nếu cần manual:
python selenium_automation.py
```

### Lỗi "FPT API failed"
- ✅ **Auto retry** - API tự động thử lại với token mới
- ✅ **Smart detection** - Phát hiện lỗi token vs lỗi khác
- ⚠️ Kiểm tra kết nối internet nếu vẫn lỗi
- ⚠️ Kiểm tra format dữ liệu JSON

### Lỗi "Re-authentication failed"
```bash
# Kiểm tra thông tin đăng nhập trong .env
cat .env

# Chạy selenium manual để debug
python selenium_automation.py

# Cập nhật Chrome driver
pip install --upgrade webdriver-manager
```

### Lỗi Selenium timeout
- Tăng timeout trong `api_server.py` (hiện tại: 120s)
- Kiểm tra Chrome browser có cài đặt không
- Kiểm tra network connection

## 📊 Logging

Logs được ghi ra console với các level:
- `INFO`: Thông tin chung
- `WARNING`: Cảnh báo
- `ERROR`: Lỗi

## 🔄 Workflow hoàn chỉnh

1. **Đăng nhập** → `selenium_automation.py`
2. **Lấy token** → `session_token.json`
3. **Khởi động API** → `api_server.py`
4. **Tạo hóa đơn** → POST `/create-sample-invoice`
5. **Lưu PDF** → `./invoices/hdn_YYYYMMDD_HHMMSS.pdf`
6. **Xem PDF** → GET `/view-invoice/{filename}`

## 📞 Hỗ trợ

- 📧 Email: <EMAIL>
- 🌐 Website: https://einvoice.fpt.com.vn

---

**⚠️ Lưu ý:**
- Đây là môi trường UAT (User Acceptance Testing)
- Token có thời hạn, cần refresh định kỳ
- Không sử dụng dữ liệu thật trong môi trường test
