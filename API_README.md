# FPT eInvoice API Server

API Server để tạo và quản lý hóa đơn điện tử thông qua FPT eInvoice API.

## 🚀 Tính năng

- ✅ **Tạo hóa đơn PDF** từ JSON data
- ✅ **Xem hóa đơn** trực tiếp trong browser
- ✅ **Liệt kê hóa đơn** đã tạo
- ✅ **Quản lý token** tự động
- ✅ **Error handling** toàn diện
- ✅ **Logging** chi tiết
- ✅ **Validation** dữ liệu đầu vào

## 📋 Yêu cầu

- Python 3.7+
- Token FPT eInvoice hợp lệ (từ `session_token.json`)
- Kết nối internet

## 🔧 Cài đặt

### 1. Cài đặt dependencies

```bash
pip install Flask requests
```

### 2. Đ<PERSON>m bảo có token

Chạy `selenium_automation.py` để lấy token:

```bash
python selenium_automation.py
```

File `session_token.json` sẽ được tạo tự động.

### 3. Khởi động server

```bash
python invoice_api_server.py
```

Server sẽ chạy tại: `http://localhost:5000`

## 📖 API Endpoints

### 1. Health Check

```http
GET /health
```

**Response:**
```json
{
  "status": "healthy",
  "timestamp": "2025-06-21T10:30:00.123456",
  "version": "1.0.0"
}
```

### 2. Tạo hóa đơn

```http
POST /create-sample-invoice
Content-Type: application/json
```

**Request Body:** (xem `sample_invoice.json`)
```json
{
  "buyer": {
    "name": "CÔNG TY TNHH ABC",
    "address": "123 Đường ABC, TP.HCM",
    "tax_code": "**********"
  },
  "seller": {
    "name": "CÔNG TY TNHH XYZ",
    "address": "456 Đường XYZ, Hà Nội",
    "tax_code": "**********"
  },
  "items": [
    {
      "name": "Sản phẩm A",
      "quantity": 1,
      "unit_price": 1000000
    }
  ],
  "amounts": {
    "subtotal": 1000000,
    "tax_amount": 100000,
    "total": 1100000
  }
}
```

**Response Success:**
```json
{
  "success": true,
  "pdf_url": "/view-invoice/hdn_20250621_143052.pdf",
  "filename": "hdn_20250621_143052.pdf",
  "created_at": "2025-06-21T14:30:52.123456"
}
```

**Response Error:**
```json
{
  "success": false,
  "error": "Dữ liệu không hợp lệ: Thiếu trường buyer.name"
}
```

### 3. Xem hóa đơn PDF

```http
GET /view-invoice/{filename}
```

**Example:**
```http
GET /view-invoice/hdn_20250621_143052.pdf
```

**Response:** PDF file (hiển thị trực tiếp trong browser)

### 4. Liệt kê hóa đơn

```http
GET /list-invoices
```

**Response:**
```json
{
  "success": true,
  "count": 2,
  "invoices": [
    {
      "filename": "hdn_20250621_143052.pdf",
      "size": 245760,
      "created_at": "2025-06-21T14:30:52.123456",
      "modified_at": "2025-06-21T14:30:52.123456",
      "view_url": "/view-invoice/hdn_20250621_143052.pdf"
    }
  ]
}
```

### 5. Thông tin token

```http
GET /token-info
```

**Response:**
```json
{
  "success": true,
  "token_info": {
    "token_type": "JWT",
    "token_source": "sessionStorage.session.token",
    "extracted_at": "2025-06-21T10:46:40.606659",
    "origin": "https://portal.einvoice.fpt.com.vn",
    "token_length": 3060,
    "has_token": true
  }
}
```

## 🧪 Testing

### Chạy test tự động

```bash
python test_api.py
```

### Test thủ công với curl

```bash
# Health check
curl http://localhost:5000/health

# Token info
curl http://localhost:5000/token-info

# Tạo hóa đơn
curl -X POST http://localhost:5000/create-sample-invoice \
  -H "Content-Type: application/json" \
  -d @sample_invoice.json

# Liệt kê hóa đơn
curl http://localhost:5000/list-invoices

# Xem PDF (mở trong browser)
curl http://localhost:5000/view-invoice/hdn_20250621_143052.pdf
```

## 📁 Cấu trúc thư mục

```
├── invoice_api_server.py    # API server chính
├── test_api.py             # Test script
├── sample_invoice.json     # Dữ liệu hóa đơn mẫu
├── session_token.json      # Token FPT eInvoice
├── invoices/              # Thư mục lưu PDF (tự động tạo)
│   ├── hdn_20250621_143052.pdf
│   └── hdn_20250621_143053.pdf
└── invoice_api.log        # Log file
```

## 🔒 Bảo mật

- ✅ **Validate filename** để tránh path traversal
- ✅ **Kiểm tra file type** (chỉ PDF)
- ✅ **Ẩn token** trong API responses
- ✅ **Request size limit** (16MB)
- ✅ **Error handling** không leak thông tin

## 🐛 Troubleshooting

### Lỗi "Không thể load token"

```bash
# Chạy lại selenium để lấy token mới
python selenium_automation.py
```

### Lỗi "FPT API failed"

- Kiểm tra token có hết hạn không
- Kiểm tra kết nối internet
- Kiểm tra format dữ liệu JSON

### Lỗi "File không tồn tại"

- Kiểm tra thư mục `./invoices/` có tồn tại không
- Kiểm tra quyền ghi file

## 📊 Logging

Logs được ghi vào:
- **Console:** Real-time logs
- **File:** `invoice_api.log`

Log levels:
- `INFO`: Thông tin chung
- `WARNING`: Cảnh báo
- `ERROR`: Lỗi

## 🔧 Cấu hình

Chỉnh sửa `CONFIG` trong `invoice_api_server.py`:

```python
CONFIG = {
    'FPT_API_BASE_URL': 'https://portal.einvoice.fpt.com.vn',
    'FPT_API_ENDPOINT': '/api/inv/pdf/view',
    'TOKEN_FILE': 'session_token.json',
    'INVOICE_DIR': './invoices',
    'REQUEST_TIMEOUT': 30,
    'MAX_RETRIES': 3
}
```

## 🚀 Production Deployment

### Sử dụng Gunicorn

```bash
pip install gunicorn
gunicorn -w 4 -b 0.0.0.0:5000 invoice_api_server:app
```

### Sử dụng Docker

```dockerfile
FROM python:3.9-slim
WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt
COPY . .
EXPOSE 5000
CMD ["gunicorn", "-w", "4", "-b", "0.0.0.0:5000", "invoice_api_server:app"]
```

## 📞 Hỗ trợ

- 📧 Email: <EMAIL>
- 📱 Hotline: 1900-xxxx
- 🌐 Website: https://einvoice.fpt.com.vn

---

**⚠️ Lưu ý:**
- API này dành cho môi trường UAT
- Không sử dụng dữ liệu thật
- Token có thời hạn, cần refresh định kỳ
